# CrabShield Configuration Guide

This guide covers all configuration options available in CrabShield.

## Configuration Overview

CrabShield uses environment variables for configuration, allowing for easy deployment across different environments. All configuration is validated at startup to ensure proper service operation.

## Core Configuration

### Database Configuration

**PostgreSQL Settings**
```bash
# Required: PostgreSQL connection string
DATABASE_URL=postgresql://username:password@localhost:5432/crabshield_db

# Optional: Connection pool settings
DATABASE_MAX_CONNECTIONS=10        # Default: 10
DATABASE_MIN_CONNECTIONS=2         # Default: 2
DATABASE_CONNECT_TIMEOUT=30        # Default: 30 seconds
DATABASE_IDLE_TIMEOUT=600          # Default: 10 minutes
```

**Database URL Format**
```
postgresql://[username[:password]@][host[:port]][/database][?param1=value1&...]
```

**SSL Parameters**
```bash
# Require SSL connection
DATABASE_URL=********************************/db?sslmode=require

# SSL with certificate verification
DATABASE_URL=********************************/db?sslmode=verify-full&sslcert=client.crt&sslkey=client.key&sslrootcert=ca.crt
```

### Redis Configuration

**Redis Settings**
```bash
# Required: Redis connection string
REDIS_URL=redis://localhost:6379

# With authentication
REDIS_URL=redis://username:password@localhost:6379

# With SSL/TLS
REDIS_URL=rediss://username:password@localhost:6379

# Optional: Connection pool settings
REDIS_MAX_CONNECTIONS=20           # Default: 20
REDIS_MIN_CONNECTIONS=5            # Default: 5
REDIS_CONNECT_TIMEOUT=10           # Default: 10 seconds
```

## Server Configuration

### Basic Server Settings

```bash
# Server binding
HOST=0.0.0.0                       # Default: 0.0.0.0
PORT=3000                          # Default: 3000

# Environment
ENVIRONMENT=production             # Options: development, staging, production

# Base URL for the service
BASE_URL=https://auth.yourdomain.com

# Logging
LOG_LEVEL=info                     # Options: trace, debug, info, warn, error
LOG_FORMAT=json                    # Options: json, pretty
```

### HTTPS and Security

```bash
# HTTPS enforcement
REQUIRE_HTTPS=true                 # Default: false
HTTPS_REDIRECT=true                # Default: false

# Security headers
SECURITY_HEADERS_ENABLED=true      # Default: true
HSTS_MAX_AGE=31536000             # Default: 1 year
```

## Authentication Configuration

### JWT Settings

```bash
# Required: JWT secret (minimum 32 characters)
JWT_SECRET=your-super-secure-jwt-secret-minimum-32-characters

# JWT configuration
JWT_ISSUER=crabshield-auth         # Default: crabshield-auth
JWT_AUDIENCE=crabshield-api        # Default: crabshield-api
JWT_ACCESS_TOKEN_TTL=900           # Default: 15 minutes
JWT_REFRESH_TOKEN_TTL=2592000      # Default: 30 days
```

**JWT Secret Requirements**
- Minimum 32 characters
- High entropy (mix of letters, numbers, symbols)
- Unique per environment
- Regularly rotated

### Password Policies

```bash
# Password requirements
PASSWORD_MIN_LENGTH=8              # Default: 8
PASSWORD_REQUIRE_UPPERCASE=true    # Default: true
PASSWORD_REQUIRE_LOWERCASE=true    # Default: true
PASSWORD_REQUIRE_NUMBERS=true      # Default: true
PASSWORD_REQUIRE_SPECIAL=true      # Default: true

# Password history
PASSWORD_HISTORY_COUNT=5           # Default: 5 (prevent reuse)
PASSWORD_MAX_AGE_DAYS=90          # Default: none (optional expiration)
```

### Account Security

```bash
# Account lockout
LOCKOUT_MAX_ATTEMPTS=5             # Default: 5
LOCKOUT_DURATION_MINUTES=15        # Default: 15
LOCKOUT_PROGRESSIVE=true           # Default: true

# Session management
SESSION_TIMEOUT_MINUTES=15         # Default: 15
SESSION_MAX_CONCURRENT=5           # Default: 5
SESSION_REMEMBER_ME_DAYS=30        # Default: 30
```

## Multi-Factor Authentication

### MFA Configuration

```bash
# MFA settings
MFA_ISSUER=CrabShield              # Default: CrabShield
MFA_TOTP_WINDOW=1                  # Default: 1 (30-second tolerance)
MFA_BACKUP_CODES_COUNT=10          # Default: 10

# Trusted devices
MFA_TRUSTED_DEVICE_TTL_DAYS=30     # Default: 30
MFA_DEVICE_FINGERPRINT_ENABLED=true # Default: true
```

## OAuth Configuration

### Google OAuth

```bash
# Google OAuth
OAUTH_GOOGLE_CLIENT_ID=your-google-client-id
OAUTH_GOOGLE_CLIENT_SECRET=your-google-client-secret
OAUTH_GOOGLE_ENABLED=true          # Default: false
OAUTH_GOOGLE_SCOPES=openid,email,profile
OAUTH_GOOGLE_REDIRECT_URIS=/auth/oauth/google/callback
```

### GitHub OAuth

```bash
# GitHub OAuth
OAUTH_GITHUB_CLIENT_ID=your-github-client-id
OAUTH_GITHUB_CLIENT_SECRET=your-github-client-secret
OAUTH_GITHUB_ENABLED=true          # Default: false
OAUTH_GITHUB_SCOPES=user:email
OAUTH_GITHUB_REDIRECT_URIS=/auth/oauth/github/callback
```

### Microsoft OAuth

```bash
# Microsoft OAuth
OAUTH_MICROSOFT_CLIENT_ID=your-microsoft-client-id
OAUTH_MICROSOFT_CLIENT_SECRET=your-microsoft-client-secret
OAUTH_MICROSOFT_ENABLED=true       # Default: false
OAUTH_MICROSOFT_SCOPES=openid,profile,email
OAUTH_MICROSOFT_REDIRECT_URIS=/auth/oauth/microsoft/callback
```

## Email Configuration

### SMTP Settings

```bash
# SMTP server configuration
SMTP_HOST=smtp.gmail.com           # Required
SMTP_PORT=587                      # Default: 587
SMTP_USERNAME=<EMAIL> # Required
SMTP_PASSWORD=your-app-password    # Required
SMTP_USE_TLS=true                  # Default: true
SMTP_USE_SSL=false                 # Default: false

# Email settings
SMTP_FROM_EMAIL=<EMAIL>
SMTP_FROM_NAME=CrabShield
SMTP_REPLY_TO=<EMAIL>
```

### Email Templates

```bash
# Template configuration
EMAIL_TEMPLATE_DIR=./templates     # Default: ./templates
EMAIL_VERIFICATION_TEMPLATE=verify_email.html
EMAIL_PASSWORD_RESET_TEMPLATE=reset_password.html
EMAIL_MFA_SETUP_TEMPLATE=mfa_setup.html
```

## Rate Limiting

### Rate Limit Configuration

```bash
# Authentication endpoints
RATE_LIMIT_AUTH_PER_MINUTE=5       # Default: 5
RATE_LIMIT_AUTH_PER_HOUR=20        # Default: 20

# Password reset
RATE_LIMIT_PASSWORD_RESET_PER_HOUR=3    # Default: 3
RATE_LIMIT_PASSWORD_RESET_PER_DAY=10    # Default: 10

# Email verification
RATE_LIMIT_EMAIL_VERIFY_PER_HOUR=3      # Default: 3
RATE_LIMIT_EMAIL_VERIFY_PER_DAY=10      # Default: 10

# General API
RATE_LIMIT_GENERAL_PER_MINUTE=100       # Default: 100
RATE_LIMIT_GENERAL_PER_HOUR=1000        # Default: 1000
```

## CORS Configuration

### CORS Settings

```bash
# CORS origins (comma-separated)
CORS_ALLOWED_ORIGINS=https://app.yourdomain.com,https://admin.yourdomain.com

# CORS methods
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS

# CORS headers
CORS_ALLOWED_HEADERS=Authorization,Content-Type,X-Requested-With

# CORS credentials
CORS_ALLOW_CREDENTIALS=true        # Default: false

# CORS max age
CORS_MAX_AGE=3600                  # Default: 3600 seconds
```

## Multi-Tenant Configuration

### Tenant Settings

```bash
# Multi-tenant mode
MULTI_TENANT_ENABLED=false         # Default: false
TENANT_ISOLATION_LEVEL=strict      # Options: strict, relaxed
TENANT_DEFAULT_LIMITS_USERS=1000   # Default: 1000
TENANT_DEFAULT_LIMITS_STORAGE_MB=100 # Default: 100MB
```

## Monitoring and Observability

### Logging Configuration

```bash
# Structured logging
LOG_LEVEL=info                     # trace, debug, info, warn, error
LOG_FORMAT=json                    # json, pretty
LOG_FILE=/var/log/crabshield.log   # Optional file output

# Request logging
LOG_REQUESTS=true                  # Default: true
LOG_REQUEST_BODY=false             # Default: false (security)
LOG_RESPONSE_BODY=false            # Default: false (security)
```

### Metrics Configuration

```bash
# Metrics endpoint
METRICS_ENABLED=true               # Default: true
METRICS_PATH=/metrics              # Default: /metrics

# Health check
HEALTH_CHECK_ENABLED=true          # Default: true
HEALTH_CHECK_PATH=/health          # Default: /health
```

## Environment-Specific Examples

### Development Environment

```bash
# .env.development
ENVIRONMENT=development
LOG_LEVEL=debug
REQUIRE_HTTPS=false
DATABASE_URL=postgresql://dev:dev@localhost:5432/crabshield_dev
REDIS_URL=redis://localhost:6379
JWT_SECRET=development-secret-minimum-32-characters
CORS_ALLOWED_ORIGINS=http://localhost:3000
```

### Staging Environment

```bash
# .env.staging
ENVIRONMENT=staging
LOG_LEVEL=info
REQUIRE_HTTPS=true
DATABASE_URL=******************************************************/crabshield_staging
REDIS_URL=redis://staging-redis:6379
JWT_SECRET=staging-secret-minimum-32-characters-different-from-prod
CORS_ALLOWED_ORIGINS=https://staging.yourdomain.com
```

### Production Environment

```bash
# .env.production
ENVIRONMENT=production
LOG_LEVEL=warn
REQUIRE_HTTPS=true
DATABASE_URL=***********************************************/crabshield_prod?sslmode=require
REDIS_URL=rediss://prod-redis:6379
JWT_SECRET=production-secret-minimum-32-characters-highly-secure
CORS_ALLOWED_ORIGINS=https://yourdomain.com
BASE_URL=https://auth.yourdomain.com

# Production security
HSTS_MAX_AGE=31536000
SECURITY_HEADERS_ENABLED=true
RATE_LIMIT_AUTH_PER_MINUTE=3
```

## Configuration Validation

CrabShield validates all configuration at startup:

### Required Variables
- `DATABASE_URL`
- `REDIS_URL`
- `JWT_SECRET` (minimum 32 characters)

### Validation Rules
- JWT secret strength validation
- Database connection testing
- Redis connection testing
- Email configuration validation (if enabled)
- OAuth configuration validation (if enabled)

### Startup Errors
If configuration is invalid, the service will fail to start with detailed error messages:

```
Error: Invalid JWT secret: must be at least 32 characters long
Error: Database connection failed: connection refused
Error: Redis connection failed: authentication failed
```

## Best Practices

### Security
1. Use strong, unique secrets for each environment
2. Enable HTTPS in production
3. Configure appropriate CORS origins
4. Set reasonable rate limits
5. Use SSL/TLS for database and Redis connections

### Performance
1. Configure appropriate connection pool sizes
2. Set reasonable session timeouts
3. Enable Redis for caching
4. Configure log levels appropriately

### Monitoring
1. Enable structured logging
2. Configure health checks
3. Set up metrics collection
4. Monitor rate limit violations

### Deployment
1. Use environment-specific configuration files
2. Never commit secrets to version control
3. Use secret management systems in production
4. Validate configuration in CI/CD pipelines
