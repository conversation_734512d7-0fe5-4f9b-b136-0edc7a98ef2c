# CrabShield Security Guide

This document outlines the comprehensive security features and best practices implemented in CrabShield.

## Security Architecture

CrabShield implements a defense-in-depth security strategy with multiple layers of protection:

1. **Network Security** - HTTPS enforcement, security headers, CORS protection
2. **Authentication Security** - Strong password policies, MFA, session management
3. **Authorization Security** - RBAC, fine-grained permissions, token validation
4. **Data Security** - Encryption at rest and in transit, data sanitization
5. **Operational Security** - Audit logging, monitoring, rate limiting

## Authentication Security

### Password Security

**Argon2 Password Hashing**
- Uses Argon2id algorithm (winner of Password Hashing Competition)
- Configurable memory cost, time cost, and parallelism
- Unique salt per password
- Resistant to GPU and ASIC attacks

**Password Policies**
```rust
pub struct PasswordPolicy {
    pub min_length: usize,              // Default: 8
    pub require_uppercase: bool,        // Default: true
    pub require_lowercase: bool,        // Default: true
    pub require_numbers: bool,          // Default: true
    pub require_special_chars: bool,    // Default: true
    pub prevent_reuse_count: usize,     // Default: 5
    pub max_age_days: Option<u32>,      // Default: None
}
```

**Password History Validation**
- Prevents reuse of last N passwords (configurable)
- Stores password hashes securely
- Automatic cleanup of old password history

### Multi-Factor Authentication (MFA)

**TOTP Implementation**
- RFC 6238 compliant Time-based One-Time Password
- 30-second time window with 1-step tolerance
- Base32 encoded secrets
- QR code generation for easy setup

**Backup Codes**
- 8-character alphanumeric codes
- Single-use only
- Securely hashed and stored
- Automatic regeneration after use

**Trusted Devices**
- Device fingerprinting based on headers and IP
- Configurable trust duration
- Automatic revocation on suspicious activity

### Session Management

**JWT Token Security**
- HS256 algorithm with strong secrets (minimum 32 characters)
- Short-lived access tokens (15 minutes default)
- Longer-lived refresh tokens with rotation
- Token blacklisting for immediate revocation

**Session Features**
- Redis-backed session storage
- Device tracking and fingerprinting
- Concurrent session limits
- Automatic cleanup of expired sessions

## Authorization Security

### Role-Based Access Control (RBAC)

**Hierarchical Roles**
- Parent-child role relationships
- Permission inheritance
- Circular dependency prevention
- Role condition support

**Fine-grained Permissions**
- Resource and action-based permissions
- Wildcard support for flexible permissions
- Permission caching for performance
- Dynamic permission evaluation

**Permission Format**
```
resource:action:scope
Examples:
- user:read:own
- admin:write:all
- billing:read:tenant
```

### API Security

**Input Validation**
- Comprehensive validation using validator crate
- XSS prevention through input sanitization
- SQL injection prevention through parameterized queries
- CSRF protection for state-changing operations

**Rate Limiting**
- Redis-backed rate limiting
- Per-IP and per-user limits
- Progressive backoff for failed attempts
- Configurable limits per endpoint

## Network Security

### HTTPS Enforcement

**TLS Configuration**
- TLS 1.2+ enforcement
- Strong cipher suites only
- HSTS headers for browser security
- Certificate validation

**Security Headers**
```rust
// Implemented security headers
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
Strict-Transport-Security: max-age=31536000; includeSubDomains
Content-Security-Policy: default-src 'self'
Referrer-Policy: strict-origin-when-cross-origin
```

### CORS Protection

**Configurable CORS**
- Whitelist of allowed origins
- Credential support control
- Method and header restrictions
- Preflight request handling

## Data Security

### Encryption

**Data at Rest**
- Database encryption using PostgreSQL TDE
- Redis encryption for cached data
- Encrypted backup storage
- Key rotation policies

**Data in Transit**
- TLS 1.2+ for all communications
- Certificate pinning for critical connections
- Encrypted internal service communication

### Data Privacy

**GDPR Compliance**
- Data minimization principles
- Right to erasure implementation
- Data portability support
- Privacy-by-design architecture

**Data Sanitization**
```rust
// Example of data masking for logs
pub fn mask_sensitive_data(data: &str, visible_chars: usize) -> String {
    // Email: <EMAIL> -> use***@***.com
    // IP: ************* -> 192.168.1.***
}
```

## Operational Security

### Audit Logging

**Comprehensive Logging**
- All authentication events
- Authorization decisions
- Administrative actions
- Security-relevant events

**Log Format**
```json
{
  "timestamp": "2024-01-01T00:00:00Z",
  "level": "INFO",
  "event": "user_login",
  "user_id": "550e8400-e29b-41d4-a716-************",
  "ip_address": "192.168.1.***",
  "user_agent": "Mozilla/5.0...",
  "success": true,
  "mfa_verified": true,
  "correlation_id": "req-123456"
}
```

### Monitoring and Alerting

**Security Metrics**
- Failed authentication attempts
- Suspicious login patterns
- Rate limit violations
- Token validation failures

**Alert Conditions**
- Multiple failed logins from same IP
- Login from new geographic location
- Privilege escalation attempts
- Unusual API usage patterns

### Account Protection

**Account Lockout**
- Progressive lockout durations
- IP-based and account-based lockout
- Automatic unlock after timeout
- Admin override capabilities

**Suspicious Activity Detection**
- Geolocation-based anomaly detection
- Device fingerprint changes
- Unusual access patterns
- Brute force attack detection

## Security Configuration

### Environment Variables

**Critical Security Settings**
```bash
# JWT Security
JWT_SECRET=minimum-32-character-secret-here

# HTTPS Enforcement
REQUIRE_HTTPS=true
HTTPS_REDIRECT=true

# CORS Configuration
CORS_ALLOWED_ORIGINS=https://yourdomain.com
CORS_ALLOW_CREDENTIALS=true

# Rate Limiting
RATE_LIMIT_AUTH_PER_MINUTE=5
RATE_LIMIT_GENERAL_PER_MINUTE=100

# Session Security
SESSION_TIMEOUT_MINUTES=15
REFRESH_TOKEN_TIMEOUT_DAYS=30
```

### Database Security

**Connection Security**
```bash
# Use SSL connections
DATABASE_URL=********************************/db?sslmode=require

# Connection pooling limits
DATABASE_MAX_CONNECTIONS=10
DATABASE_MIN_CONNECTIONS=2
```

**Database Hardening**
- Principle of least privilege for database users
- Regular security updates
- Network isolation
- Backup encryption

### Redis Security

**Redis Configuration**
```bash
# Authentication
REDIS_URL=redis://username:password@host:6379

# SSL/TLS
REDIS_TLS=true

# Connection limits
REDIS_MAX_CONNECTIONS=20
```

## Security Best Practices

### Development

1. **Secure Coding**
   - Input validation on all user inputs
   - Output encoding to prevent XSS
   - Parameterized queries to prevent SQL injection
   - Proper error handling without information disclosure

2. **Dependency Management**
   - Regular security audits with `cargo audit`
   - Keep dependencies updated
   - Review third-party code
   - Use minimal dependency sets

3. **Testing**
   - Security-focused unit tests
   - Integration tests for authentication flows
   - Penetration testing
   - Code review processes

### Deployment

1. **Infrastructure Security**
   - Network segmentation
   - Firewall configuration
   - Regular security updates
   - Intrusion detection systems

2. **Secrets Management**
   - Use dedicated secret management systems
   - Rotate secrets regularly
   - Never commit secrets to version control
   - Environment-specific configurations

3. **Monitoring**
   - Real-time security monitoring
   - Log aggregation and analysis
   - Automated alerting
   - Regular security assessments

## Incident Response

### Security Incident Handling

1. **Detection**
   - Automated monitoring alerts
   - User reports
   - Security audit findings

2. **Response**
   - Immediate threat containment
   - Impact assessment
   - Evidence preservation
   - Communication plan

3. **Recovery**
   - System restoration
   - Security improvements
   - Post-incident review
   - Documentation updates

### Emergency Procedures

**Account Compromise**
1. Immediately revoke all user tokens
2. Force password reset
3. Disable account if necessary
4. Investigate access logs
5. Notify user of security incident

**System Compromise**
1. Isolate affected systems
2. Preserve evidence
3. Assess data exposure
4. Implement containment measures
5. Begin recovery procedures

## Compliance

### Standards Compliance

- **OWASP Top 10** - Protection against common web vulnerabilities
- **NIST Cybersecurity Framework** - Comprehensive security controls
- **ISO 27001** - Information security management
- **SOC 2 Type II** - Security, availability, and confidentiality controls

### Regulatory Compliance

- **GDPR** - European data protection regulation
- **CCPA** - California consumer privacy act
- **HIPAA** - Healthcare information protection (when applicable)
- **PCI DSS** - Payment card industry standards (when applicable)

## Security Updates

### Vulnerability Management

1. **Regular Updates**
   - Monthly security patches
   - Quarterly dependency updates
   - Annual security assessments

2. **Vulnerability Disclosure**
   - Responsible disclosure policy
   - Security advisory process
   - CVE tracking and response

3. **Security Communication**
   - Security bulletins
   - User notifications
   - Documentation updates

For security issues or questions, please contact: <EMAIL>
