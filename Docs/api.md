# CrabShield API Reference

This document provides comprehensive API documentation for the CrabShield authentication service.

## Base URL

```
https://your-domain.com/api/v1
```

## Authentication

Most endpoints require authentication via JWT tokens. Include the token in the Authorization header:

```
Authorization: Bearer <jwt_token>
```

## Response Format

All API responses follow a consistent format:

### Success Response
```json
{
  "success": true,
  "data": { ... },
  "message": "Operation completed successfully"
}
```

### Error Response
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input provided",
    "details": { ... }
  }
}
```

## Authentication Endpoints

### Register User

Creates a new user account with email verification.

**Endpoint:** `POST /auth/register`

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "first_name": "<PERSON>",
  "last_name": "<PERSON><PERSON>"
}
```

**Response (201 Created):**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "550e8400-e29b-41d4-a716-************",
      "email": "<EMAIL>",
      "first_name": "John",
      "last_name": "Doe",
      "email_verified": false,
      "mfa_enabled": false,
      "created_at": "2024-01-01T00:00:00Z"
    },
    "verification_email_sent": true
  },
  "message": "User registered successfully. Please check your email for verification."
}
```

**Validation Rules:**
- Email: Valid email format, unique
- Password: Minimum 8 characters, must contain uppercase, lowercase, number, and special character
- First/Last Name: 1-50 characters, letters and spaces only

### Login

Authenticates a user and returns JWT tokens.

**Endpoint:** `POST /auth/login`

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "mfa_code": "123456",  // Optional, required if MFA is enabled
  "remember_me": true    // Optional, extends session duration
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_in": 900,  // 15 minutes
    "token_type": "Bearer",
    "user": {
      "id": "550e8400-e29b-41d4-a716-************",
      "email": "<EMAIL>",
      "first_name": "John",
      "last_name": "Doe",
      "email_verified": true,
      "mfa_enabled": true
    }
  },
  "message": "Login successful"
}
```

**MFA Required Response (202 Accepted):**
```json
{
  "success": false,
  "data": {
    "mfa_required": true,
    "mfa_token": "temp-mfa-token-for-verification",
    "available_methods": ["TOTP", "BACKUP_CODE"]
  },
  "message": "MFA verification required"
}
```

### Logout

Invalidates the current session and optionally all user sessions.

**Endpoint:** `POST /auth/logout`

**Headers:**
```
Authorization: Bearer <jwt_token>
X-Logout-All-Devices: true  // Optional, logout from all devices
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "logged_out_from_all_devices": true
  },
  "message": "Logged out from all devices successfully"
}
```

### Verify Token

Validates a JWT token and returns user information.

**Endpoint:** `POST /auth/verify`

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "valid": true,
    "user": {
      "id": "550e8400-e29b-41d4-a716-************",
      "email": "use***@***.com",  // Masked for privacy
      "mfa_verified": true,
      "trusted_device": false
    }
  },
  "message": "Token is valid"
}
```

### Refresh Token

Exchanges a refresh token for new access and refresh tokens.

**Endpoint:** `POST /auth/refresh`

**Request Body:**
```json
{
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_in": 900,
    "token_type": "Bearer"
  },
  "message": "Token refreshed successfully"
}
```

## Multi-Factor Authentication (MFA)

### Setup MFA

Initiates MFA setup and returns TOTP secret and QR code.

**Endpoint:** `POST /mfa/setup`

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Request Body:**
```json
{
  "device_name": "My iPhone"  // Optional
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "secret": "JBSWY3DPEHPK3PXP",
    "qr_code": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
    "backup_codes": [
      "12345678",
      "87654321",
      "11223344",
      "44332211",
      "55667788"
    ],
    "manual_entry_key": "JBSW Y3DP EHPK 3PXP"
  },
  "message": "MFA setup initiated. Please scan the QR code with your authenticator app."
}
```

### Verify MFA Setup

Completes MFA setup by verifying a TOTP code.

**Endpoint:** `POST /mfa/verify-setup`

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Request Body:**
```json
{
  "totp_code": "123456"
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "mfa_enabled": true,
    "backup_codes_remaining": 5
  },
  "message": "MFA setup completed successfully"
}
```

### Verify MFA

Verifies MFA code during login process.

**Endpoint:** `POST /mfa/verify`

**Headers:**
```
Authorization: Bearer <mfa_token>
```

**Request Body:**
```json
{
  "code": "123456",
  "verification_type": "TOTP",  // or "BACKUP_CODE"
  "trust_device": true          // Optional
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_in": 900,
    "token_type": "Bearer",
    "trusted_device": true
  },
  "message": "MFA verification successful"
}
```

### Get MFA Status

Returns current MFA configuration for the user.

**Endpoint:** `GET /mfa/status`

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "mfa_enabled": true,
    "backup_codes_remaining": 3,
    "trusted_devices": [
      {
        "id": "device-uuid",
        "name": "My iPhone",
        "last_used": "2024-01-01T00:00:00Z",
        "created_at": "2023-12-01T00:00:00Z"
      }
    ]
  },
  "message": "MFA status retrieved successfully"
}
```

### Disable MFA

Disables MFA for the user account.

**Endpoint:** `DELETE /mfa/disable`

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Request Body:**
```json
{
  "password": "current-password"
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "mfa_enabled": false
  },
  "message": "MFA disabled successfully"
}

## OAuth Endpoints

### Initiate OAuth Flow

Redirects user to OAuth provider for authentication.

**Endpoint:** `GET /oauth/{provider}/authorize`

**Parameters:**
- `provider`: google, github, microsoft
- `redirect_uri`: Optional, must be pre-configured
- `state`: Optional CSRF protection token

**Example:**
```
GET /oauth/google/authorize?redirect_uri=https://yourapp.com/callback&state=csrf-token
```

**Response:** HTTP 302 redirect to provider

### Handle OAuth Callback

Processes OAuth callback and creates/authenticates user.

**Endpoint:** `POST /oauth/{provider}/callback`

**Request Body:**
```json
{
  "code": "oauth-authorization-code",
  "state": "csrf-state-token"
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_in": 900,
    "token_type": "Bearer",
    "user": {
      "id": "550e8400-e29b-41d4-a716-************",
      "email": "<EMAIL>",
      "first_name": "John",
      "last_name": "Doe",
      "oauth_provider": "google",
      "email_verified": true
    }
  },
  "message": "OAuth authentication successful"
}
```

## Password Management

### Request Password Reset

Sends password reset email to user.

**Endpoint:** `POST /password/reset`

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "reset_token_sent": true,
    "rate_limit_info": {
      "attempts_remaining": 2,
      "reset_time": "2024-01-01T01:00:00Z",
      "retry_after_seconds": 3600
    }
  },
  "message": "Password reset email sent successfully"
}
```

### Confirm Password Reset

Resets password using token from email.

**Endpoint:** `POST /password/reset/confirm`

**Request Body:**
```json
{
  "token": "reset-token-from-email",
  "new_password": "NewSecurePassword123!"
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "password_reset": true
  },
  "message": "Password reset successfully"
}
```

### Change Password

Changes password for authenticated user.

**Endpoint:** `POST /password/change`

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Request Body:**
```json
{
  "current_password": "CurrentPassword123!",
  "new_password": "NewSecurePassword123!"
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "password_changed": true
  },
  "message": "Password changed successfully"
}
```

## Email Verification

### Send Verification Email

Sends email verification link to user.

**Endpoint:** `POST /email/send-verification`

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "resend": false
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "verification_email_sent": true,
    "rate_limit": {
      "attempts_remaining": 2,
      "reset_time": "2024-01-01T01:00:00Z",
      "window_duration_seconds": 3600
    }
  },
  "message": "Verification email sent successfully"
}
```

### Verify Email

Verifies email using token from email.

**Endpoint:** `POST /email/verify`

**Request Body:**
```json
{
  "token": "verification-token-from-email"
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "email_verified": true,
    "user": {
      "id": "550e8400-e29b-41d4-a716-************",
      "email": "<EMAIL>",
      "email_verified": true
    }
  },
  "message": "Email verified successfully"
}
```

## Health Check

### Service Health

Returns service health status.

**Endpoint:** `GET /health`

**Response (200 OK):**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00Z",
  "version": "1.0.0",
  "services": {
    "database": {
      "status": "healthy",
      "response_time_ms": 5
    },
    "redis": {
      "status": "healthy",
      "response_time_ms": 2
    },
    "email": {
      "status": "healthy",
      "response_time_ms": 100
    }
  }
}
```

## Error Codes

| Code | Description |
|------|-------------|
| `VALIDATION_ERROR` | Request validation failed |
| `AUTHENTICATION_FAILED` | Invalid credentials |
| `AUTHORIZATION_FAILED` | Insufficient permissions |
| `MFA_REQUIRED` | MFA verification needed |
| `RATE_LIMIT_EXCEEDED` | Too many requests |
| `USER_NOT_FOUND` | User does not exist |
| `EMAIL_NOT_VERIFIED` | Email verification required |
| `ACCOUNT_LOCKED` | Account temporarily locked |
| `TOKEN_EXPIRED` | JWT token has expired |
| `TOKEN_INVALID` | JWT token is invalid |
| `INTERNAL_ERROR` | Server error occurred |

## Rate Limiting

API endpoints are rate limited to prevent abuse:

- **Authentication endpoints**: 5 requests per minute per IP
- **Password reset**: 3 requests per hour per email
- **Email verification**: 3 requests per hour per email
- **General endpoints**: 100 requests per minute per user

Rate limit headers are included in responses:
```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: **********
```
```
