# CrabShield Deployment Guide

This guide covers deployment strategies and best practices for CrabShield in production environments.

## Deployment Overview

CrabShield is designed for cloud-native deployment with support for:
- Docker containers
- Kubernetes orchestration
- Cloud platforms (AWS, GCP, Azure)
- Traditional server deployment

## Prerequisites

### System Requirements

**Minimum Requirements**
- CPU: 2 cores
- RAM: 2GB
- Storage: 10GB
- Network: 1Gbps

**Recommended for Production**
- CPU: 4+ cores
- RAM: 8GB+
- Storage: 50GB+ SSD
- Network: 10Gbps

### Dependencies

**Required Services**
- PostgreSQL 13+ (with SSL support)
- Redis 6+ (with persistence)
- SMTP server (for email functionality)

**Optional Services**
- Load balancer (nginx, HAProxy, cloud LB)
- Monitoring (Prometheus, Grafana)
- Log aggregation (ELK stack, Fluentd)

## Docker Deployment

### Building the Image

```dockerfile
# Dockerfile
FROM rust:1.70 as builder

WORKDIR /app
COPY . .
RUN cargo build --release

FROM debian:bookworm-slim

RUN apt-get update && apt-get install -y \
    ca-certificates \
    libssl3 \
    && rm -rf /var/lib/apt/lists/*

COPY --from=builder /app/target/release/crabshield /usr/local/bin/crabshield

EXPOSE 3000
CMD ["crabshield"]
```

**Build Commands**
```bash
# Build the image
docker build -t crabshield:latest .

# Multi-platform build
docker buildx build --platform linux/amd64,linux/arm64 -t crabshield:latest .
```

### Docker Compose

```yaml
# docker-compose.yml
version: '3.8'

services:
  crabshield:
    image: crabshield:latest
    ports:
      - "3000:3000"
    environment:
      - DATABASE_URL=**********************************************/crabshield_db
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=${JWT_SECRET}
      - ENVIRONMENT=production
      - REQUIRE_HTTPS=true
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  postgres:
    image: postgres:15
    environment:
      - POSTGRES_DB=crabshield_db
      - POSTGRES_USER=crabshield
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - crabshield
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
```

### Environment Configuration

```bash
# .env.production
JWT_SECRET=your-super-secure-jwt-secret-minimum-32-characters
POSTGRES_PASSWORD=secure-postgres-password
REDIS_PASSWORD=secure-redis-password
SMTP_PASSWORD=your-smtp-password
```

## Kubernetes Deployment

### Namespace and ConfigMap

```yaml
# namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: crabshield

---
# configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: crabshield-config
  namespace: crabshield
data:
  ENVIRONMENT: "production"
  LOG_LEVEL: "info"
  HOST: "0.0.0.0"
  PORT: "3000"
  REQUIRE_HTTPS: "true"
  CORS_ALLOWED_ORIGINS: "https://yourdomain.com"
```

### Secrets

```yaml
# secrets.yaml
apiVersion: v1
kind: Secret
metadata:
  name: crabshield-secrets
  namespace: crabshield
type: Opaque
data:
  JWT_SECRET: <base64-encoded-jwt-secret>
  DATABASE_URL: <base64-encoded-database-url>
  REDIS_URL: <base64-encoded-redis-url>
  SMTP_PASSWORD: <base64-encoded-smtp-password>
```

### Deployment

```yaml
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: crabshield
  namespace: crabshield
spec:
  replicas: 3
  selector:
    matchLabels:
      app: crabshield
  template:
    metadata:
      labels:
        app: crabshield
    spec:
      containers:
      - name: crabshield
        image: crabshield:latest
        ports:
        - containerPort: 3000
        envFrom:
        - configMapRef:
            name: crabshield-config
        - secretRef:
            name: crabshield-secrets
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
```

### Service and Ingress

```yaml
# service.yaml
apiVersion: v1
kind: Service
metadata:
  name: crabshield-service
  namespace: crabshield
spec:
  selector:
    app: crabshield
  ports:
  - port: 80
    targetPort: 3000
  type: ClusterIP

---
# ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: crabshield-ingress
  namespace: crabshield
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
spec:
  tls:
  - hosts:
    - auth.yourdomain.com
    secretName: crabshield-tls
  rules:
  - host: auth.yourdomain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: crabshield-service
            port:
              number: 80
```

## Cloud Platform Deployment

### AWS Deployment

**Using ECS Fargate**
```json
{
  "family": "crabshield",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "512",
  "memory": "1024",
  "executionRoleArn": "arn:aws:iam::account:role/ecsTaskExecutionRole",
  "taskRoleArn": "arn:aws:iam::account:role/ecsTaskRole",
  "containerDefinitions": [
    {
      "name": "crabshield",
      "image": "your-account.dkr.ecr.region.amazonaws.com/crabshield:latest",
      "portMappings": [
        {
          "containerPort": 3000,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "ENVIRONMENT",
          "value": "production"
        }
      ],
      "secrets": [
        {
          "name": "JWT_SECRET",
          "valueFrom": "arn:aws:secretsmanager:region:account:secret:crabshield/jwt-secret"
        }
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/crabshield",
          "awslogs-region": "us-west-2",
          "awslogs-stream-prefix": "ecs"
        }
      }
    }
  ]
}
```

**Using RDS and ElastiCache**
```bash
# Environment variables for AWS services
DATABASE_URL=postgresql://username:<EMAIL>:5432/crabshield
REDIS_URL=redis://crabshield-cache.xyz.cache.amazonaws.com:6379
```

### Google Cloud Platform

**Using Cloud Run**
```yaml
# cloudrun.yaml
apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: crabshield
  annotations:
    run.googleapis.com/ingress: all
spec:
  template:
    metadata:
      annotations:
        autoscaling.knative.dev/maxScale: "10"
        run.googleapis.com/cpu-throttling: "false"
    spec:
      containerConcurrency: 100
      containers:
      - image: gcr.io/your-project/crabshield:latest
        ports:
        - containerPort: 3000
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: crabshield-secrets
              key: database-url
        resources:
          limits:
            cpu: "1"
            memory: "1Gi"
```

### Azure Container Instances

```yaml
# azure-container-group.yaml
apiVersion: 2019-12-01
location: eastus
name: crabshield
properties:
  containers:
  - name: crabshield
    properties:
      image: your-registry.azurecr.io/crabshield:latest
      ports:
      - port: 3000
        protocol: TCP
      environmentVariables:
      - name: ENVIRONMENT
        value: production
      - name: JWT_SECRET
        secureValue: your-jwt-secret
      resources:
        requests:
          cpu: 1
          memoryInGB: 2
  osType: Linux
  ipAddress:
    type: Public
    ports:
    - port: 3000
      protocol: TCP
```

## Load Balancer Configuration

### Nginx Configuration

```nginx
# nginx.conf
upstream crabshield {
    server crabshield-1:3000;
    server crabshield-2:3000;
    server crabshield-3:3000;
}

server {
    listen 80;
    server_name auth.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name auth.yourdomain.com;

    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;

    # Security headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection "1; mode=block";

    location / {
        proxy_pass http://crabshield;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Health check
        location /health {
            access_log off;
            proxy_pass http://crabshield;
        }
    }
}
```

## Database Setup

### PostgreSQL Configuration

```sql
-- Create database and user
CREATE DATABASE crabshield_db;
CREATE USER crabshield WITH ENCRYPTED PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE crabshield_db TO crabshield;

-- Enable required extensions
\c crabshield_db;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
```

### Database Migrations

```bash
# Run migrations
export DATABASE_URL=postgresql://crabshield:password@localhost:5432/crabshield_db
sqlx migrate run

# Verify migration status
sqlx migrate info
```

## Monitoring and Observability

### Prometheus Configuration

```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
- job_name: 'crabshield'
  static_configs:
  - targets: ['crabshield:3000']
  metrics_path: /metrics
  scrape_interval: 30s
```

### Grafana Dashboard

```json
{
  "dashboard": {
    "title": "CrabShield Metrics",
    "panels": [
      {
        "title": "Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])",
            "legendFormat": "{{method}} {{status}}"
          }
        ]
      },
      {
        "title": "Authentication Success Rate",
        "type": "stat",
        "targets": [
          {
            "expr": "rate(auth_attempts_total{result=\"success\"}[5m]) / rate(auth_attempts_total[5m])",
            "legendFormat": "Success Rate"
          }
        ]
      }
    ]
  }
}
```

## Security Considerations

### Network Security

1. **Firewall Rules**
   - Allow only necessary ports (80, 443, 22)
   - Restrict database access to application servers
   - Use VPC/private networks

2. **SSL/TLS Configuration**
   - Use strong cipher suites
   - Enable HSTS
   - Regular certificate renewal

### Application Security

1. **Secrets Management**
   - Use dedicated secret management systems
   - Rotate secrets regularly
   - Never log sensitive information

2. **Container Security**
   - Use minimal base images
   - Regular security updates
   - Scan images for vulnerabilities

## Backup and Disaster Recovery

### Database Backup

```bash
# Automated backup script
#!/bin/bash
BACKUP_DIR="/backups"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="crabshield_backup_${DATE}.sql"

pg_dump $DATABASE_URL > "${BACKUP_DIR}/${BACKUP_FILE}"
gzip "${BACKUP_DIR}/${BACKUP_FILE}"

# Upload to cloud storage
aws s3 cp "${BACKUP_DIR}/${BACKUP_FILE}.gz" s3://your-backup-bucket/
```

### Redis Backup

```bash
# Redis backup
redis-cli --rdb /backups/redis_backup_$(date +%Y%m%d_%H%M%S).rdb
```

## Troubleshooting

### Common Issues

1. **Database Connection Issues**
   ```bash
   # Test database connectivity
   psql $DATABASE_URL -c "SELECT 1;"
   ```

2. **Redis Connection Issues**
   ```bash
   # Test Redis connectivity
   redis-cli -u $REDIS_URL ping
   ```

3. **SSL Certificate Issues**
   ```bash
   # Check certificate expiration
   openssl x509 -in cert.pem -text -noout | grep "Not After"
   ```

### Log Analysis

```bash
# View application logs
docker logs crabshield

# Search for errors
docker logs crabshield 2>&1 | grep ERROR

# Monitor real-time logs
docker logs -f crabshield
```

## Performance Optimization

### Application Tuning

1. **Connection Pooling**
   - Optimize database connection pool size
   - Configure Redis connection limits
   - Monitor connection usage

2. **Caching Strategy**
   - Enable Redis caching for sessions
   - Cache RBAC permissions
   - Implement cache warming

### Infrastructure Scaling

1. **Horizontal Scaling**
   - Add more application instances
   - Use load balancers
   - Implement auto-scaling

2. **Database Scaling**
   - Use read replicas
   - Implement connection pooling
   - Consider database sharding

This deployment guide provides a comprehensive foundation for deploying CrabShield in production environments. Adjust configurations based on your specific requirements and infrastructure.
