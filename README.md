# CrabShield 🦀🛡️

A comprehensive, production-ready authentication and authorization service built with Rust and Actix Web. CrabShield provides secure user management, multi-factor authentication, role-based access control, and OAuth integration with enterprise-grade security features.

## ✨ Features

### 🔐 Core Authentication
- **Secure User Registration & Login** - Argon2 password hashing with configurable policies
- **Multi-Factor Authentication (MFA)** - TOTP support with backup codes and trusted devices
- **Session Management** - JWT-based authentication with refresh tokens and session tracking
- **Password Security** - Comprehensive password policies, strength analysis, and history validation
- **Account Lockout** - Intelligent lockout mechanisms with progressive delays

### 🛡️ Authorization & Access Control
- **Role-Based Access Control (RBAC)** - Hierarchical roles with permission inheritance and caching
- **Fine-grained Permissions** - Resource and action-based permission system
- **Multi-tenant Support** - Isolated tenant environments with custom configurations
- **API Key Management** - Service-to-service authentication with scoped permissions

### 🔒 Security Features
- **Rate Limiting** - Advanced rate limiting with Redis-backed storage
- **Input Validation** - Comprehensive validation and sanitization with XSS protection
- **Email Verification** - Secure email verification with configurable templates
- **Password Reset** - Secure password reset flow with time-limited tokens
- **Audit Logging** - Comprehensive security event logging with data sanitization
- **IP Tracking** - Real client IP extraction with proxy support
- **Token Blacklisting** - JWT token revocation with Redis-based blacklist

### 🌐 Integration & OAuth
- **OAuth 2.0 Support** - Google, GitHub, Microsoft with dynamic configuration
- **PKCE Support** - Enhanced security for OAuth flows
- **Dynamic Redirect URIs** - Configurable and validated redirect URIs
- **Provider-specific Features** - Custom scopes and additional fields per provider

### 🚀 Operational Features
- **Health Monitoring** - Built-in health checks and metrics
- **Configuration Management** - Environment-based configuration with validation
- **Database Migrations** - Automated database schema management
- **Docker Support** - Production-ready containerization
- **Observability** - Structured logging with tracing and metrics
- **HTTPS Enforcement** - TLS/SSL support with security headers

## 🚀 Quick Start

### Prerequisites
- Rust 1.70+ 
- PostgreSQL 13+
- Redis 6+
- Docker & Docker Compose (optional)

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-org/crabshield.git
   cd crabshield
   ```

2. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Start dependencies with Docker**
   ```bash
   docker-compose up -d postgres redis
   ```

4. **Run database migrations**
   ```bash
   cargo run --bin migrate
   ```

5. **Start the service**
   ```bash
   cargo run
   ```

The service will be available at `http://localhost:3000`

### Docker Deployment

```bash
# Build and start all services
docker-compose up -d

# View logs
docker-compose logs -f crabshield
```

## ⚙️ Configuration

CrabShield uses environment variables for configuration. See [Configuration Guide](docs/configuration.md) for detailed options.

### Essential Environment Variables

```bash
# Database
DATABASE_URL=postgresql://user:password@localhost:5432/crabshield_db

# Redis
REDIS_URL=redis://localhost:6379

# JWT (IMPORTANT: Use a strong secret in production)
JWT_SECRET=your-super-secure-jwt-secret-minimum-32-characters

# Server
HOST=0.0.0.0
PORT=3000
BASE_URL=https://yourdomain.com

# Security
REQUIRE_HTTPS=true
CORS_ALLOWED_ORIGINS=https://yourdomain.com

# Email (optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password

# OAuth (optional)
OAUTH_GOOGLE_CLIENT_ID=your-google-client-id
OAUTH_GOOGLE_CLIENT_SECRET=your-google-client-secret
OAUTH_GOOGLE_ENABLED=true
OAUTH_GOOGLE_SCOPES=openid,email,profile
OAUTH_GOOGLE_REDIRECT_URIS=/auth/oauth/google/callback
```

## 📚 API Documentation

### Authentication Endpoints

```bash
# Register a new user
POST /api/v1/auth/register
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "first_name": "John",
  "last_name": "Doe"
}

# Login
POST /api/v1/auth/login
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "mfa_code": "123456",  // Optional, if MFA is enabled
  "remember_me": true    // Optional, for extended session
}

# Logout (supports logout from all devices)
POST /api/v1/auth/logout
Authorization: Bearer <jwt_token>
X-Logout-All-Devices: true  // Optional header

# Verify token
POST /api/v1/auth/verify
Authorization: Bearer <jwt_token>

# Refresh token
POST /api/v1/auth/refresh
{
  "refresh_token": "your-refresh-token"
}
```

### MFA Endpoints

```bash
# Setup MFA
POST /api/v1/mfa/setup
Authorization: Bearer <jwt_token>
{
  "device_name": "My Phone"  // Optional
}

# Verify MFA setup
POST /api/v1/mfa/verify-setup
Authorization: Bearer <jwt_token>
{
  "totp_code": "123456"
}

# Verify MFA during login
POST /api/v1/mfa/verify
Authorization: Bearer <mfa_token>
{
  "code": "123456",
  "verification_type": "TOTP",
  "trust_device": true  // Optional
}

# Disable MFA
DELETE /api/v1/mfa/disable
Authorization: Bearer <jwt_token>
{
  "password": "current-password"
}

# Get MFA status
GET /api/v1/mfa/status
Authorization: Bearer <jwt_token>
```

### OAuth Endpoints

```bash
# Initiate OAuth flow
GET /api/v1/oauth/{provider}/authorize?redirect_uri=https://yourapp.com/callback

# Handle OAuth callback
POST /api/v1/oauth/{provider}/callback
{
  "code": "oauth-authorization-code",
  "state": "csrf-state-token"
}

# Supported providers: google, github, microsoft
```

### Password Management

```bash
# Request password reset
POST /api/v1/password/reset
{
  "email": "<EMAIL>"
}

# Confirm password reset
POST /api/v1/password/reset/confirm
{
  "token": "reset-token",
  "new_password": "NewSecurePassword123!"
}

# Change password (authenticated)
POST /api/v1/password/change
Authorization: Bearer <jwt_token>
{
  "current_password": "CurrentPassword123!",
  "new_password": "NewSecurePassword123!"
}
```

For complete API documentation, see [API Reference](docs/api.md) or visit `/docs` when the service is running.

## 🔒 Security

CrabShield implements industry-standard security practices:

### ✅ Security Features Implemented
- **Password Security**: Argon2 hashing with configurable parameters and history validation
- **JWT Security**: Secure token generation with blacklisting and rotation support
- **Rate Limiting**: Advanced rate limiting with Redis backend and IP-based tracking
- **Input Validation**: Comprehensive validation and sanitization with XSS protection
- **HTTPS Enforcement**: TLS/SSL support with security headers middleware
- **Audit Logging**: Detailed security event logging with data sanitization
- **OWASP Compliance**: Following OWASP security guidelines
- **IP Extraction**: Proper client IP extraction with proxy header support
- **GDPR Compliance**: Data sanitization and privacy-compliant logging

### 🛡️ Security Configuration
- Minimum 32-character JWT secrets with strength validation
- Configurable password policies with complexity requirements
- Rate limiting with progressive delays and IP-based tracking
- Session management with device fingerprinting
- Token blacklisting for immediate revocation

See [Security Guide](docs/security.md) for detailed security information.

## 🧪 Development

### Running Tests

```bash
# Run all tests
cargo test

# Run with coverage
cargo tarpaulin --out html

# Run integration tests
cargo test --test integration

# Run specific service tests
cargo test rbac_service
cargo test password_service
```

### Code Quality

```bash
# Format code
cargo fmt

# Lint code
cargo clippy

# Security audit
cargo audit

# Check compilation
cargo check
```

### Database Management

```bash
# Create new migration
sqlx migrate add create_users_table

# Run migrations
sqlx migrate run

# Revert last migration
sqlx migrate revert
```

## 🚀 Deployment

### Production Checklist

- [ ] Set strong JWT secret (32+ characters, validated at startup)
- [ ] Configure HTTPS/TLS with proper certificates
- [ ] Set up proper CORS origins for your domain
- [ ] Configure rate limiting appropriate for your traffic
- [ ] Set up monitoring and structured logging
- [ ] Configure Redis for session and cache storage
- [ ] Set up database connection pooling
- [ ] Configure email service for verification and reset
- [ ] Review and test OAuth provider configurations
- [ ] Set up backup and disaster recovery
- [ ] Configure security headers and HTTPS enforcement

### Environment-Specific Configuration

```bash
# Production
ENVIRONMENT=production
REQUIRE_HTTPS=true
LOG_LEVEL=info

# Staging
ENVIRONMENT=staging
REQUIRE_HTTPS=false
LOG_LEVEL=debug

# Development
ENVIRONMENT=development
REQUIRE_HTTPS=false
LOG_LEVEL=trace
```

See [Deployment Guide](docs/deployment.md) for detailed deployment instructions.

## 📊 Monitoring

CrabShield provides comprehensive monitoring capabilities:

- **Health Checks**: `/health` endpoint for load balancer health checks
- **Metrics**: Prometheus-compatible metrics at `/metrics`
- **Structured Logging**: JSON-formatted logs with correlation IDs and data sanitization
- **Tracing**: Distributed tracing support with OpenTelemetry
- **Security Events**: Comprehensive audit logging for security monitoring

## 🤝 Contributing

We welcome contributions! Please see [Contributing Guide](CONTRIBUTING.md) for details.

### Development Setup

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add comprehensive tests
5. Run the test suite and ensure all tests pass
6. Submit a pull request with detailed description

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: [docs/](docs/)
- **Issues**: [GitHub Issues](https://github.com/your-org/crabshield/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-org/crabshield/discussions)
- **Security**: See [SECURITY.md](SECURITY.md) for security policy

## 🗺️ Roadmap

### Completed ✅
- Core authentication and authorization
- Multi-factor authentication with TOTP
- OAuth 2.0 integration with major providers
- Comprehensive security features
- Rate limiting and session management
- Password history and security validation
- Token blacklisting and revocation
- Input validation and sanitization
- GDPR-compliant logging

### Upcoming 🚧
- [ ] WebAuthn/FIDO2 support
- [ ] Advanced fraud detection
- [ ] GraphQL API
- [ ] Mobile SDK
- [ ] Advanced analytics dashboard
- [ ] SAML 2.0 support
- [ ] Biometric authentication
- [ ] Advanced threat detection

---

Built with ❤️ in Rust 🦀

**Production Ready** • **Security First** • **Highly Scalable**
