use actix_web::{
    dev::{forward_ready, Service, ServiceRequest, ServiceResponse, Transform},
    Error, HttpResponse, Result, HttpMessage, body::BoxBody,
    http::header::HeaderValue,
};
use futures_util::future::LocalBoxFuture;
use std::{
    collections::HashMap,
    future::{ready, Ready},
    rc::Rc,
    time::{Duration, Instant},
};
use tracing::{warn, debug};
use crate::utils::errors::AppError;

/// Rate limiting configuration for different endpoints
#[derive(Debug, Clone)]
pub struct RateLimitConfig {
    pub requests_per_window: u32,
    pub window_duration: Duration,
    pub burst_allowance: u32,
}

impl Default for RateLimitConfig {
    fn default() -> Self {
        Self {
            requests_per_window: 100,
            window_duration: Duration::from_secs(60),
            burst_allowance: 10,
        }
    }
}

/// Rate limiting middleware with sliding window algorithm
pub struct RateLimiting {
    global_config: RateLimitConfig,
    endpoint_configs: HashMap<String, RateLimitConfig>,
    enable_ip_based: bool,
    enable_user_based: bool,
}

impl RateLimiting {
    pub fn new() -> Self {
        Self {
            global_config: RateLimitConfig::default(),
            endpoint_configs: HashMap::new(),
            enable_ip_based: true,
            enable_user_based: true,
        }
    }

    pub fn global_config(mut self, config: RateLimitConfig) -> Self {
        self.global_config = config;
        self
    }

    pub fn endpoint_config<S: Into<String>>(mut self, endpoint: S, config: RateLimitConfig) -> Self {
        self.endpoint_configs.insert(endpoint.into(), config);
        self
    }

    pub fn ip_based(mut self, enabled: bool) -> Self {
        self.enable_ip_based = enabled;
        self
    }

    pub fn user_based(mut self, enabled: bool) -> Self {
        self.enable_user_based = enabled;
        self
    }

    /// Configure strict rate limits for authentication endpoints
    pub fn auth_endpoints(mut self) -> Self {
        let auth_config = RateLimitConfig {
            requests_per_window: 5,
            window_duration: Duration::from_secs(300), // 5 minutes
            burst_allowance: 2,
        };

        self.endpoint_configs.insert("/api/auth/login".to_string(), auth_config.clone());
        self.endpoint_configs.insert("/api/auth/register".to_string(), auth_config.clone());
        self.endpoint_configs.insert("/api/auth/reset-password".to_string(), auth_config.clone());
        self.endpoint_configs.insert("/api/auth/verify-mfa".to_string(), auth_config);
        self
    }

    /// Configure moderate rate limits for general API endpoints
    pub fn api_endpoints(mut self) -> Self {
        let api_config = RateLimitConfig {
            requests_per_window: 1000,
            window_duration: Duration::from_secs(3600), // 1 hour
            burst_allowance: 50,
        };

        self.endpoint_configs.insert("/api/".to_string(), api_config);
        self
    }
}

impl Default for RateLimiting {
    fn default() -> Self {
        Self::new()
    }
}

/// Request tracking for sliding window rate limiting
#[derive(Debug)]
struct RequestTracker {
    requests: Vec<Instant>,
    last_cleanup: Instant,
}

impl RequestTracker {
    fn new() -> Self {
        Self {
            requests: Vec::new(),
            last_cleanup: Instant::now(),
        }
    }

    fn add_request(&mut self, now: Instant, window_duration: Duration) -> bool {
        // Clean up old requests periodically
        if now.duration_since(self.last_cleanup) > Duration::from_secs(60) {
            self.cleanup(now, window_duration);
            self.last_cleanup = now;
        }

        // Remove requests outside the window
        let cutoff = now - window_duration;
        self.requests.retain(|&request_time| request_time > cutoff);

        // Add current request
        self.requests.push(now);
        true
    }

    fn count_requests(&self, now: Instant, window_duration: Duration) -> usize {
        let cutoff = now - window_duration;
        self.requests.iter().filter(|&&request_time| request_time > cutoff).count()
    }

    fn cleanup(&mut self, now: Instant, window_duration: Duration) {
        let cutoff = now - window_duration;
        self.requests.retain(|&request_time| request_time > cutoff);
    }
}

impl<S> Transform<S, ServiceRequest> for RateLimiting
where
    S: Service<ServiceRequest, Response = ServiceResponse<BoxBody>, Error = Error> + 'static,
    S::Future: 'static,
{
    type Response = ServiceResponse<BoxBody>;
    type Error = Error;
    type Transform = RateLimitingMiddleware<S>;
    type InitError = ();
    type Future = Ready<Result<Self::Transform, Self::InitError>>;

    fn new_transform(&self, service: S) -> Self::Future {
        ready(Ok(RateLimitingMiddleware {
            service: Rc::new(service),
            global_config: self.global_config.clone(),
            endpoint_configs: self.endpoint_configs.clone(),
            enable_ip_based: self.enable_ip_based,
            enable_user_based: self.enable_user_based,
            ip_trackers: Rc::new(std::sync::Mutex::new(HashMap::new())),
            user_trackers: Rc::new(std::sync::Mutex::new(HashMap::new())),
        }))
    }
}

pub struct RateLimitingMiddleware<S> {
    service: Rc<S>,
    global_config: RateLimitConfig,
    endpoint_configs: HashMap<String, RateLimitConfig>,
    enable_ip_based: bool,
    enable_user_based: bool,
    ip_trackers: Rc<std::sync::Mutex<HashMap<String, RequestTracker>>>,
    user_trackers: Rc<std::sync::Mutex<HashMap<String, RequestTracker>>>,
}

impl<S> Service<ServiceRequest> for RateLimitingMiddleware<S>
where
    S: Service<ServiceRequest, Response = ServiceResponse<BoxBody>, Error = Error> + 'static,
    S::Future: 'static,
{
    type Response = ServiceResponse<BoxBody>;
    type Error = Error;
    type Future = LocalBoxFuture<'static, Result<Self::Response, Self::Error>>;

    forward_ready!(service);

    fn call(&self, req: ServiceRequest) -> Self::Future {
        let service = self.service.clone();
        let global_config = self.global_config.clone();
        let endpoint_configs = self.endpoint_configs.clone();
        let enable_ip_based = self.enable_ip_based;
        let enable_user_based = self.enable_user_based;
        let ip_trackers = self.ip_trackers.clone();
        let user_trackers = self.user_trackers.clone();

        Box::pin(async move {
            let now = Instant::now();
            let path = req.path();

            // Find the most specific rate limit config for this endpoint
            let config = endpoint_configs
                .iter()
                .filter(|(endpoint, _)| path.starts_with(endpoint.as_str()))
                .max_by_key(|(endpoint, _)| endpoint.len())
                .map(|(_, config)| config)
                .unwrap_or(&global_config);

            // Check IP-based rate limiting
            if enable_ip_based {
                if let Some(ip) = get_client_ip(&req) {
                    if let Ok(mut trackers) = ip_trackers.lock() {
                        let tracker = trackers.entry(ip.clone()).or_insert_with(RequestTracker::new);
                        let request_count = tracker.count_requests(now, config.window_duration);

                        if request_count >= config.requests_per_window as usize {
                            warn!("Rate limit exceeded for IP {}: {} requests in {:?}", 
                                  ip, request_count, config.window_duration);
                            
                            return Ok(create_rate_limit_response(
                                req,
                                config,
                                request_count,
                                "IP rate limit exceeded"
                            ));
                        }

                        tracker.add_request(now, config.window_duration);
                    }
                }
            }

            // Check user-based rate limiting (if authenticated)
            if enable_user_based {
                if let Some(user_id) = get_user_id(&req) {
                    if let Ok(mut trackers) = user_trackers.lock() {
                        let tracker = trackers.entry(user_id.clone()).or_insert_with(RequestTracker::new);
                        let request_count = tracker.count_requests(now, config.window_duration);

                        if request_count >= config.requests_per_window as usize {
                            warn!("Rate limit exceeded for user {}: {} requests in {:?}", 
                                  user_id, request_count, config.window_duration);
                            
                            return Ok(create_rate_limit_response(
                                req,
                                config,
                                request_count,
                                "User rate limit exceeded"
                            ));
                        }

                        tracker.add_request(now, config.window_duration);
                    }
                }
            }

            debug!("Rate limit check passed for {}", path);

            // Add rate limit headers to response
            let mut response = service.call(req).await?;
            add_rate_limit_headers(&mut response, config);

            Ok(response)
        })
    }
}

/// Extract client IP address from request
fn get_client_ip(req: &ServiceRequest) -> Option<String> {
    crate::utils::network::extract_client_ip_from_service_request(req)
}

/// Extract user ID from authenticated request
fn get_user_id(req: &ServiceRequest) -> Option<String> {
    // Try to get user ID from JWT claims in request extensions
    if let Some(claims) = req.extensions().get::<crate::services::jwt_service::Claims>() {
        return Some(claims.user_id.to_string());
    }

    None
}

/// Create rate limit exceeded response
fn create_rate_limit_response(
    req: ServiceRequest,
    config: &RateLimitConfig,
    current_count: usize,
    message: &str,
) -> ServiceResponse<BoxBody> {
    let error = AppError::TooManyRequests(message.to_string());

    req.into_response(
        HttpResponse::TooManyRequests()
            .insert_header(("X-RateLimit-Limit", config.requests_per_window.to_string()))
            .insert_header(("X-RateLimit-Remaining", "0"))
            .insert_header(("X-RateLimit-Reset", config.window_duration.as_secs().to_string()))
            .insert_header(("Retry-After", config.window_duration.as_secs().to_string()))
            .json(serde_json::json!({
                "error": "rate_limit_exceeded",
                "message": error.to_string(),
                "limit": config.requests_per_window,
                "window_seconds": config.window_duration.as_secs(),
                "current_count": current_count
            }))
    )
}

/// Add rate limit headers to successful responses
fn add_rate_limit_headers(response: &mut ServiceResponse<BoxBody>, config: &RateLimitConfig) {
    let headers = response.headers_mut();
    headers.insert(
        actix_web::http::header::HeaderName::from_static("x-ratelimit-limit"),
        HeaderValue::from_str(&config.requests_per_window.to_string()).unwrap(),
    );
    headers.insert(
        actix_web::http::header::HeaderName::from_static("x-ratelimit-window"),
        HeaderValue::from_str(&config.window_duration.as_secs().to_string()).unwrap(),
    );
}

/// Create rate limiting middleware with authentication-focused configuration
pub fn create_auth_rate_limiting() -> RateLimiting {
    RateLimiting::new()
        .auth_endpoints()
        .api_endpoints()
        .ip_based(true)
        .user_based(true)
}

/// Create rate limiting middleware for development (more permissive)
pub fn create_dev_rate_limiting() -> RateLimiting {
    RateLimiting::new()
        .global_config(RateLimitConfig {
            requests_per_window: 10000,
            window_duration: Duration::from_secs(3600),
            burst_allowance: 100,
        })
        .ip_based(true)
        .user_based(false) // Disabled for easier testing
}
