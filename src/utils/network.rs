use actix_web::{HttpRequest, dev::ServiceRequest};
use std::net::IpAddr;
use tracing::{warn, debug};

/// Extract client IP address from HTTP request
/// 
/// This function attempts to extract the real client IP address by checking
/// various headers in order of preference:
/// 1. X-Forwarded-For (for proxy/load balancer setups)
/// 2. X-Real-IP (for nginx proxy setups)
/// 3. Connection peer address (direct connection)
/// 
/// # Arguments
/// * `req` - The HTTP request to extract IP from
/// 
/// # Returns
/// * `Option<String>` - The client IP address as a string, or None if extraction fails
/// 
/// # Security Notes
/// - X-Forwarded-For can be spoofed by clients, but is trusted when behind a proxy
/// - Always validate that requests are coming through your trusted proxy infrastructure
/// - Consider implementing IP whitelist validation for critical operations
pub fn extract_client_ip_from_request(req: &HttpRequest) -> Option<String> {
    // Check X-Forwarded-For header first (for proxy setups)
    if let Some(forwarded_for) = req.headers().get("x-forwarded-for") {
        if let Ok(forwarded_str) = forwarded_for.to_str() {
            // Take the first IP from the comma-separated list (original client)
            if let Some(first_ip) = forwarded_str.split(',').next() {
                let ip = first_ip.trim();
                if let Ok(parsed_ip) = ip.parse::<IpAddr>() {
                    debug!("Extracted IP from X-Forwarded-For: {}", parsed_ip);
                    return Some(parsed_ip.to_string());
                } else {
                    warn!("Invalid IP format in X-Forwarded-For header: {}", ip);
                }
            }
        }
    }

    // Check X-Real-IP header (nginx proxy)
    if let Some(real_ip) = req.headers().get("x-real-ip") {
        if let Ok(ip_str) = real_ip.to_str() {
            if let Ok(parsed_ip) = ip_str.parse::<IpAddr>() {
                debug!("Extracted IP from X-Real-IP: {}", parsed_ip);
                return Some(parsed_ip.to_string());
            } else {
                warn!("Invalid IP format in X-Real-IP header: {}", ip_str);
            }
        }
    }

    // Fall back to connection info (direct connection)
    if let Some(peer_addr) = req.connection_info().peer_addr() {
        if let Ok(parsed_ip) = peer_addr.parse::<IpAddr>() {
            debug!("Extracted IP from connection info: {}", parsed_ip);
            return Some(parsed_ip.to_string());
        } else {
            warn!("Invalid IP format in connection info: {}", peer_addr);
        }
    }

    warn!("Failed to extract client IP from request");
    None
}

/// Extract client IP address from ServiceRequest (for middleware)
/// 
/// This is a variant of `extract_client_ip_from_request` that works with
/// `ServiceRequest` objects used in middleware.
/// 
/// # Arguments
/// * `req` - The service request to extract IP from
/// 
/// # Returns
/// * `Option<String>` - The client IP address as a string, or None if extraction fails
pub fn extract_client_ip_from_service_request(req: &ServiceRequest) -> Option<String> {
    // Check X-Forwarded-For header first (for proxy setups)
    if let Some(forwarded) = req.headers().get("x-forwarded-for") {
        if let Ok(forwarded_str) = forwarded.to_str() {
            if let Some(first_ip) = forwarded_str.split(',').next() {
                let ip = first_ip.trim();
                if let Ok(parsed_ip) = ip.parse::<IpAddr>() {
                    debug!("Extracted IP from X-Forwarded-For: {}", parsed_ip);
                    return Some(parsed_ip.to_string());
                } else {
                    warn!("Invalid IP format in X-Forwarded-For header: {}", ip);
                }
            }
        }
    }

    // Check X-Real-IP header
    if let Some(real_ip) = req.headers().get("x-real-ip") {
        if let Ok(ip_str) = real_ip.to_str() {
            if let Ok(parsed_ip) = ip_str.parse::<IpAddr>() {
                debug!("Extracted IP from X-Real-IP: {}", parsed_ip);
                return Some(parsed_ip.to_string());
            } else {
                warn!("Invalid IP format in X-Real-IP header: {}", ip_str);
            }
        }
    }

    // Fall back to connection info
    if let Some(peer_addr) = req.peer_addr() {
        let ip = peer_addr.ip().to_string();
        debug!("Extracted IP from connection info: {}", ip);
        return Some(ip);
    }

    warn!("Failed to extract client IP from service request");
    None
}

/// Extract client IP address and return as IpAddr type
/// 
/// This function is similar to `extract_client_ip_from_request` but returns
/// a parsed `IpAddr` instead of a string.
/// 
/// # Arguments
/// * `req` - The HTTP request to extract IP from
/// 
/// # Returns
/// * `Option<IpAddr>` - The client IP address as IpAddr, or None if extraction fails
pub fn extract_client_ip_addr(req: &HttpRequest) -> Option<IpAddr> {
    extract_client_ip_from_request(req)
        .and_then(|ip_str| ip_str.parse::<IpAddr>().ok())
}

/// Sanitize IP address for logging
/// 
/// This function masks parts of IP addresses for privacy compliance
/// while retaining enough information for security analysis.
/// 
/// # Arguments
/// * `ip` - The IP address to sanitize
/// 
/// # Returns
/// * `String` - The sanitized IP address
/// 
/// # Examples
/// * IPv4: "***********00" -> "192.168.1.***"
/// * IPv6: "2001:db8::1" -> "2001:db8::***"
pub fn sanitize_ip_for_logging(ip: &str) -> String {
    if let Ok(parsed_ip) = ip.parse::<IpAddr>() {
        match parsed_ip {
            IpAddr::V4(ipv4) => {
                let octets = ipv4.octets();
                format!("{}.{}.{}.***", octets[0], octets[1], octets[2])
            }
            IpAddr::V6(_) => {
                // For IPv6, show only the first 64 bits (network portion)
                if let Some(colon_pos) = ip.rfind("::") {
                    format!("{}::***", &ip[..colon_pos])
                } else {
                    // If no :: found, mask the last segment
                    let segments: Vec<&str> = ip.split(':').collect();
                    if segments.len() > 1 {
                        let masked_segments = &segments[..segments.len()-1];
                        format!("{}:***", masked_segments.join(":"))
                    } else {
                        "***".to_string()
                    }
                }
            }
        }
    } else {
        "***".to_string()
    }
}

/// Validate if an IP address is from a trusted proxy
/// 
/// This function checks if the given IP address is from a known trusted proxy.
/// This is important for security when processing X-Forwarded-For headers.
/// 
/// # Arguments
/// * `ip` - The IP address to validate
/// * `trusted_proxies` - List of trusted proxy IP addresses/ranges
/// 
/// # Returns
/// * `bool` - True if the IP is from a trusted proxy
/// 
/// # Note
/// This is a basic implementation. In production, you should implement
/// proper CIDR range checking for proxy networks.
pub fn is_trusted_proxy(ip: &str, trusted_proxies: &[String]) -> bool {
    trusted_proxies.iter().any(|proxy| proxy == ip)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_sanitize_ipv4() {
        assert_eq!(sanitize_ip_for_logging("***********00"), "192.168.1.***");
        assert_eq!(sanitize_ip_for_logging("********"), "10.0.0.***");
    }

    #[test]
    fn test_sanitize_ipv6() {
        assert_eq!(sanitize_ip_for_logging("2001:db8::1"), "2001:db8::***");
        assert_eq!(sanitize_ip_for_logging("::1"), "::***");
    }

    #[test]
    fn test_sanitize_invalid_ip() {
        assert_eq!(sanitize_ip_for_logging("invalid-ip"), "***");
        assert_eq!(sanitize_ip_for_logging(""), "***");
    }

    #[test]
    fn test_is_trusted_proxy() {
        let trusted_proxies = vec!["***********".to_string(), "********".to_string()];
        assert!(is_trusted_proxy("***********", &trusted_proxies));
        assert!(!is_trusted_proxy("***********", &trusted_proxies));
    }
}
