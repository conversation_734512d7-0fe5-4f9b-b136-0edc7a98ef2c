use regex::Regex;
use std::sync::OnceLock;
use tracing::warn;

/// Regex for validating TOTP codes (exactly 6 digits)
pub static TOTP_CODE_REGEX: OnceLock<Regex> = OnceLock::new();

/// Validation errors for input data
#[derive(Debug, thiserror::Error)]
pub enum ValidationError {
    #[error("Invalid format: {0}")]
    InvalidFormat(String),
    #[error("Invalid length: {0}")]
    InvalidLength(String),
    #[error("Invalid characters: {0}")]
    InvalidCharacters(String),
    #[error("Required field missing: {0}")]
    Required(String),
}

/// Email validation using a comprehensive regex pattern
/// 
/// This regex follows RFC 5322 guidelines and validates most common email formats
/// while being strict enough to prevent common injection attacks.
pub fn validate_email(email: &str) -> Result<(), ValidationError> {
    static EMAIL_REGEX: OnceLock<Regex> = OnceLock::new();
    let regex = EMAIL_REGEX.get_or_init(|| {
        Regex::new(r"^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$")
            .expect("Invalid email regex")
    });

    if email.is_empty() {
        return Err(ValidationError::Required("email".to_string()));
    }

    if email.len() > 254 {
        return Err(ValidationError::InvalidLength("Email address too long (max 254 characters)".to_string()));
    }

    if !regex.is_match(email) {
        return Err(ValidationError::InvalidFormat("Invalid email format".to_string()));
    }

    // Additional security checks
    if email.contains("..") {
        return Err(ValidationError::InvalidFormat("Email contains consecutive dots".to_string()));
    }

    if email.starts_with('.') || email.ends_with('.') {
        return Err(ValidationError::InvalidFormat("Email cannot start or end with a dot".to_string()));
    }

    Ok(())
}

/// Validate TOTP code format
///
/// TOTP codes should be exactly 6 digits (0-9) with no other characters.
/// This validation ensures the code meets the expected format before
/// attempting cryptographic verification.
pub fn validate_totp_code(code: &str) -> Result<(), ValidationError> {
    let regex = TOTP_CODE_REGEX.get_or_init(|| {
        Regex::new(r"^\d{6}$").expect("Invalid TOTP regex")
    });

    if code.is_empty() {
        return Err(ValidationError::Required("TOTP code".to_string()));
    }

    if code.len() != 6 {
        return Err(ValidationError::InvalidLength("TOTP code must be exactly 6 digits".to_string()));
    }

    if !regex.is_match(code) {
        return Err(ValidationError::InvalidCharacters("TOTP code must contain only digits".to_string()));
    }

    Ok(())
}

/// Validate backup code format
/// 
/// Backup codes are typically 8-character alphanumeric codes.
/// This validation ensures the format is correct.
pub fn validate_backup_code(code: &str) -> Result<(), ValidationError> {
    if code.is_empty() {
        return Err(ValidationError::Required("Backup code".to_string()));
    }

    if code.len() != 8 {
        return Err(ValidationError::InvalidLength("Backup code must be exactly 8 characters".to_string()));
    }

    if !code.chars().all(|c| c.is_ascii_alphanumeric()) {
        return Err(ValidationError::InvalidCharacters("Backup code must contain only letters and numbers".to_string()));
    }

    Ok(())
}

/// Validate username format
/// 
/// Usernames should be 3-30 characters, alphanumeric with underscores and hyphens allowed.
/// No spaces or special characters that could cause issues.
pub fn validate_username(username: &str) -> Result<(), ValidationError> {
    if username.is_empty() {
        return Err(ValidationError::Required("username".to_string()));
    }

    if username.len() < 3 {
        return Err(ValidationError::InvalidLength("Username must be at least 3 characters".to_string()));
    }

    if username.len() > 30 {
        return Err(ValidationError::InvalidLength("Username must be no more than 30 characters".to_string()));
    }

    static USERNAME_REGEX: OnceLock<Regex> = OnceLock::new();
    let regex = USERNAME_REGEX.get_or_init(|| {
        Regex::new(r"^[a-zA-Z0-9_-]+$")
            .expect("Invalid username regex")
    });

    if !regex.is_match(username) {
        return Err(ValidationError::InvalidCharacters("Username can only contain letters, numbers, underscores, and hyphens".to_string()));
    }

    // Additional checks
    if username.starts_with('-') || username.ends_with('-') {
        return Err(ValidationError::InvalidFormat("Username cannot start or end with a hyphen".to_string()));
    }

    if username.starts_with('_') || username.ends_with('_') {
        return Err(ValidationError::InvalidFormat("Username cannot start or end with an underscore".to_string()));
    }

    Ok(())
}

/// Validate password strength
/// 
/// This is a basic validation that checks for minimum requirements.
/// The actual password policy is enforced by the PasswordService.
pub fn validate_password_format(password: &str) -> Result<(), ValidationError> {
    if password.is_empty() {
        return Err(ValidationError::Required("password".to_string()));
    }

    if password.len() < 8 {
        return Err(ValidationError::InvalidLength("Password must be at least 8 characters".to_string()));
    }

    if password.len() > 128 {
        return Err(ValidationError::InvalidLength("Password must be no more than 128 characters".to_string()));
    }

    // Check for null bytes or control characters
    if password.chars().any(|c| c.is_control()) {
        return Err(ValidationError::InvalidCharacters("Password contains invalid characters".to_string()));
    }

    Ok(())
}

/// Validate device name for MFA trusted devices
/// 
/// Device names should be reasonable length and contain safe characters.
pub fn validate_device_name(name: &str) -> Result<(), ValidationError> {
    if name.is_empty() {
        return Err(ValidationError::Required("device name".to_string()));
    }

    if name.len() > 100 {
        return Err(ValidationError::InvalidLength("Device name must be no more than 100 characters".to_string()));
    }

    // Allow letters, numbers, spaces, and common punctuation
    static DEVICE_NAME_REGEX: OnceLock<Regex> = OnceLock::new();
    let regex = DEVICE_NAME_REGEX.get_or_init(|| {
        Regex::new(r"^[a-zA-Z0-9\s\-_.,()]+$")
            .expect("Invalid device name regex")
    });

    if !regex.is_match(name) {
        return Err(ValidationError::InvalidCharacters("Device name contains invalid characters".to_string()));
    }

    Ok(())
}

/// Validate OAuth provider name
/// 
/// Provider names should be from a known list of supported providers.
pub fn validate_oauth_provider(provider: &str) -> Result<(), ValidationError> {
    const SUPPORTED_PROVIDERS: &[&str] = &["google", "github", "microsoft", "apple"];

    if provider.is_empty() {
        return Err(ValidationError::Required("OAuth provider".to_string()));
    }

    if !SUPPORTED_PROVIDERS.contains(&provider.to_lowercase().as_str()) {
        return Err(ValidationError::InvalidFormat(format!("Unsupported OAuth provider: {}", provider)));
    }

    Ok(())
}

/// Sanitize string input for safe logging
/// 
/// This function removes or replaces characters that could cause issues
/// in log files or when displayed in UIs.
pub fn sanitize_for_logging(input: &str) -> String {
    input
        .chars()
        .filter(|c| !c.is_control() || *c == '\n' || *c == '\t')
        .take(200) // Limit length for logging
        .collect()
}

/// Validate and sanitize user input strings
/// 
/// This function performs basic sanitization to prevent common injection attacks
/// while preserving legitimate user input.
pub fn sanitize_user_input(input: &str, max_length: usize) -> Result<String, ValidationError> {
    if input.len() > max_length {
        return Err(ValidationError::InvalidLength(format!("Input too long (max {} characters)", max_length)));
    }

    // Remove null bytes and most control characters
    let sanitized: String = input
        .chars()
        .filter(|c| !matches!(c, '\0'..='\x08' | '\x0B'..='\x0C' | '\x0E'..='\x1F' | '\x7F'))
        .collect();

    // Check for potentially dangerous patterns
    let dangerous_patterns = ["<script", "javascript:", "data:", "vbscript:", "onload=", "onerror="];
    let input_lower = sanitized.to_lowercase();
    
    for pattern in &dangerous_patterns {
        if input_lower.contains(pattern) {
            warn!("Potentially dangerous pattern detected in user input: {}", pattern);
            return Err(ValidationError::InvalidCharacters("Input contains potentially dangerous content".to_string()));
        }
    }

    Ok(sanitized)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_validate_email() {
        assert!(validate_email("<EMAIL>").is_ok());
        assert!(validate_email("<EMAIL>").is_ok());
        assert!(validate_email("").is_err());
        assert!(validate_email("invalid-email").is_err());
        assert!(validate_email("user@").is_err());
        assert!(validate_email("@domain.com").is_err());
    }

    #[test]
    fn test_validate_totp_code() {
        assert!(validate_totp_code("123456").is_ok());
        assert!(validate_totp_code("000000").is_ok());
        assert!(validate_totp_code("").is_err());
        assert!(validate_totp_code("12345").is_err());
        assert!(validate_totp_code("1234567").is_err());
        assert!(validate_totp_code("12345a").is_err());
        assert!(validate_totp_code("abcdef").is_err());
    }

    #[test]
    fn test_validate_backup_code() {
        assert!(validate_backup_code("ABC12345").is_ok());
        assert!(validate_backup_code("12345678").is_ok());
        assert!(validate_backup_code("").is_err());
        assert!(validate_backup_code("1234567").is_err());
        assert!(validate_backup_code("123456789").is_err());
        assert!(validate_backup_code("ABC123!@").is_err());
    }

    #[test]
    fn test_validate_username() {
        assert!(validate_username("user123").is_ok());
        assert!(validate_username("test_user").is_ok());
        assert!(validate_username("user-name").is_ok());
        assert!(validate_username("").is_err());
        assert!(validate_username("ab").is_err());
        assert!(validate_username("user@name").is_err());
        assert!(validate_username("-username").is_err());
        assert!(validate_username("username-").is_err());
    }

    #[test]
    fn test_validate_oauth_provider() {
        assert!(validate_oauth_provider("google").is_ok());
        assert!(validate_oauth_provider("GitHub").is_ok()); // Case insensitive
        assert!(validate_oauth_provider("").is_err());
        assert!(validate_oauth_provider("facebook").is_err()); // Not supported
    }
}
