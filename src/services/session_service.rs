use crate::models::{UserSession, CreateSessionRequest, SessionResponse};
use anyhow::Result;
use chrono::{DateTime, Duration, Utc};
use redis::{Commands, Connection};
use serde::{Deserialize, Serialize};
use sqlx::{PgPool, Row};
use std::collections::HashMap;
use thiserror::Error;
use tracing::{error, info};
use uuid::Uuid;

#[derive(Error, Debug)]
pub enum SessionError {
    #[error("Database error: {0}")]
    DatabaseError(String),
    #[error("Redis error: {0}")]
    RedisError(String),
    #[error("Session not found")]
    SessionNotFound,
    #[error("Session expired")]
    SessionExpired,
    #[error("Session already invalidated")]
    SessionInvalidated,
    #[error("Maximum concurrent sessions exceeded")]
    MaxSessionsExceeded,
    #[error("Invalid session token")]
    InvalidToken,
    #[error("Device not trusted")]
    DeviceNotTrusted,
    #[error("MFA verification required")]
    MfaRequired,
    #[error("Validation error: {0}")]
    ValidationError(String),
    #[error("Internal error: {0}")]
    InternalError(String),
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SessionPolicy {
    pub default_session_duration: Duration,
    pub max_session_duration: Duration,
    pub max_concurrent_sessions: u32,
    pub trusted_device_session_duration: Duration,
    pub activity_timeout: Duration,
    pub cleanup_interval: Duration,
    pub require_mfa_for_sensitive_operations: bool,
    pub enable_device_tracking: bool,
    pub enable_location_tracking: bool,
}

impl Default for SessionPolicy {
    fn default() -> Self {
        Self {
            default_session_duration: Duration::hours(8),
            max_session_duration: Duration::days(30),
            max_concurrent_sessions: 5,
            trusted_device_session_duration: Duration::days(30),
            activity_timeout: Duration::minutes(30),
            cleanup_interval: Duration::hours(1),
            require_mfa_for_sensitive_operations: true,
            enable_device_tracking: true,
            enable_location_tracking: true,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SessionMetrics {
    pub total_active_sessions: u64,
    pub sessions_by_user: HashMap<Uuid, u32>,
    pub sessions_by_device: HashMap<String, u32>,
    pub expired_sessions_cleaned: u64,
    pub last_cleanup_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DeviceInfo {
    pub fingerprint: String,
    pub name: Option<String>,
    pub user_agent: Option<String>,
    pub is_trusted: bool,
    pub first_seen: DateTime<Utc>,
    pub last_seen: DateTime<Utc>,
    pub session_count: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SessionActivity {
    pub session_id: Uuid,
    pub user_id: Uuid,
    pub activity_type: String,
    pub ip_address: Option<String>,
    pub user_agent: Option<String>,
    pub timestamp: DateTime<Utc>,
    pub metadata: Option<serde_json::Value>,
}

#[derive(Clone)]
pub struct SessionService {
    db_pool: PgPool,
    redis_url: String,
    policy: SessionPolicy,
}

impl SessionService {
    pub fn new(db_pool: PgPool, redis_url: String) -> Self {
        Self {
            db_pool,
            redis_url,
            policy: SessionPolicy::default(),
        }
    }

    pub fn with_policy(mut self, policy: SessionPolicy) -> Self {
        self.policy = policy;
        self
    }

    /// Create a new session for a user
    pub async fn create_session(
        &self,
        request: CreateSessionRequest,
    ) -> Result<UserSession, SessionError> {
        // Validate request
        self.validate_create_session_request(&request)?;

        // Check concurrent session limits
        self.check_concurrent_session_limits(request.user_id).await?;

        // Generate session tokens
        let session_token = self.generate_session_token();
        let refresh_token = self.generate_refresh_token();

        // Determine session duration based on device trust
        let session_duration = if request.is_trusted_device {
            self.policy.trusted_device_session_duration
        } else {
            self.policy.default_session_duration
        };

        let expires_at = Utc::now() + session_duration;
        let session_id = Uuid::new_v4();

        // Create session object
        let session = UserSession {
            id: session_id,
            user_id: request.user_id,
            session_token: session_token.clone(),
            refresh_token: refresh_token.clone(),
            device_fingerprint: request.device_fingerprint.clone(),
            device_name: request.device_name.clone(),
            ip_address: request.ip_address.clone(),
            user_agent: request.user_agent.clone(),
            location_country: request.location_country.clone(),
            location_city: request.location_city.clone(),
            expires_at,
            last_activity_at: Utc::now(),
            is_active: true,
            is_trusted_device: request.is_trusted_device,
            requires_mfa: request.requires_mfa,
            mfa_verified_at: None,
            created_at: Utc::now(),
            invalidated_at: None,
            invalidated_reason: None,
        };

        // Store session in database
        self.store_session_in_db(&session).await?;

        // Store session in Redis for fast access
        self.store_session_in_redis(&session).await?;

        // Update device tracking
        if self.policy.enable_device_tracking {
            self.update_device_tracking(&session).await?;
        }

        // Log session creation
        self.log_session_activity(&session, "session_created", None).await?;

        info!(
            "Session created for user {} with session_id {}",
            request.user_id, session_id
        );

        Ok(session)
    }

    /// Validate a session token and return session info
    pub async fn validate_session(&self, session_token: &str) -> Result<UserSession, SessionError> {
        // Try Redis first for performance
        if let Ok(session) = self.get_session_from_redis(session_token).await {
            // Check if session is still valid
            if self.is_session_valid(&session) {
                // Update last activity
                self.update_session_activity(session.id).await?;
                return Ok(session);
            } else {
                // Session expired or invalid, remove from Redis
                self.remove_session_from_redis(session_token).await?;
            }
        }

        // Fallback to database
        let session = self.get_session_from_db(session_token).await?;
        
        if !self.is_session_valid(&session) {
            return Err(SessionError::SessionExpired);
        }

        // Update last activity
        self.update_session_activity(session.id).await?;

        // Refresh Redis cache
        self.store_session_in_redis(&session).await?;

        Ok(session)
    }

    /// Refresh a session using refresh token
    pub async fn refresh_session(&self, refresh_token: &str) -> Result<UserSession, SessionError> {
        // Get session by refresh token
        let mut session = self.get_session_by_refresh_token(refresh_token).await?;

        if !session.is_active {
            return Err(SessionError::SessionInvalidated);
        }

        // Generate new tokens
        session.session_token = self.generate_session_token();
        session.refresh_token = self.generate_refresh_token();
        
        // Extend expiration
        let session_duration = if session.is_trusted_device {
            self.policy.trusted_device_session_duration
        } else {
            self.policy.default_session_duration
        };
        
        session.expires_at = Utc::now() + session_duration;
        session.last_activity_at = Utc::now();

        // Update in database and Redis
        self.update_session_in_db(&session).await?;
        self.store_session_in_redis(&session).await?;

        // Log session refresh
        self.log_session_activity(&session, "session_refreshed", None).await?;

        info!("Session refreshed for user {} with session_id {}", session.user_id, session.id);

        Ok(session)
    }

    /// Invalidate a session (logout)
    pub async fn invalidate_session(
        &self,
        session_token: &str,
        reason: Option<String>,
    ) -> Result<(), SessionError> {
        let session = match self.get_session_from_redis(session_token).await {
            Ok(session) => session,
            Err(_) => self.get_session_from_db(session_token).await?,
        };

        self.invalidate_session_by_id(session.id, reason).await
    }

    /// Invalidate session by ID
    pub async fn invalidate_session_by_id(
        &self,
        session_id: Uuid,
        reason: Option<String>,
    ) -> Result<(), SessionError> {
        // Update database
        let query = r#"
            UPDATE user_sessions 
            SET is_active = FALSE, 
                invalidated_at = NOW(), 
                invalidated_reason = $2
            WHERE id = $1
        "#;

        sqlx::query(query)
            .bind(session_id)
            .bind(reason.as_deref())
            .execute(&self.db_pool)
            .await
            .map_err(|e| SessionError::DatabaseError(e.to_string()))?;

        // Remove from Redis
        self.remove_session_from_redis_by_id(session_id).await?;

        // Log session invalidation
        if let Ok(session) = self.get_session_by_id(session_id).await {
            self.log_session_activity(&session, "session_invalidated", 
                Some(serde_json::json!({"reason": reason}))
            ).await?;
        }

        info!("Session {} invalidated with reason: {:?}", session_id, reason);

        Ok(())
    }

    /// Invalidate all sessions for a user
    pub async fn invalidate_all_user_sessions(
        &self,
        user_id: Uuid,
        reason: Option<String>,
    ) -> Result<u64, SessionError> {
        // Get all active sessions for user
        let sessions = self.get_user_sessions(user_id).await?;
        let mut invalidated_count = 0;

        for session in sessions {
            if session.is_active {
                self.invalidate_session_by_id(session.id, reason.clone()).await?;
                invalidated_count += 1;
            }
        }

        info!("Invalidated {} sessions for user {}", invalidated_count, user_id);

        Ok(invalidated_count)
    }

    /// Get all sessions for a user
    pub async fn get_user_sessions(&self, user_id: Uuid) -> Result<Vec<UserSession>, SessionError> {
        let query = r#"
            SELECT * FROM user_sessions
            WHERE user_id = $1
            ORDER BY last_activity_at DESC
        "#;

        sqlx::query_as::<_, UserSession>(query)
            .bind(user_id)
            .fetch_all(&self.db_pool)
            .await
            .map_err(|e| SessionError::DatabaseError(e.to_string()))
    }

    /// Get active sessions for a user
    pub async fn get_active_user_sessions(&self, user_id: Uuid) -> Result<Vec<SessionResponse>, SessionError> {
        let query = r#"
            SELECT * FROM user_sessions
            WHERE user_id = $1 AND is_active = TRUE AND expires_at > NOW()
            ORDER BY last_activity_at DESC
        "#;

        let sessions = sqlx::query_as::<_, UserSession>(query)
            .bind(user_id)
            .fetch_all(&self.db_pool)
            .await
            .map_err(|e| SessionError::DatabaseError(e.to_string()))?;

        Ok(sessions.into_iter().map(SessionResponse::from).collect())
    }

    /// Update session activity timestamp
    pub async fn update_session_activity(&self, session_id: Uuid) -> Result<(), SessionError> {
        // Update database
        let query = r#"
            UPDATE user_sessions
            SET last_activity_at = NOW()
            WHERE id = $1 AND is_active = TRUE
        "#;

        sqlx::query(query)
            .bind(session_id)
            .execute(&self.db_pool)
            .await
            .map_err(|e| SessionError::DatabaseError(e.to_string()))?;

        // Update Redis cache
        self.update_session_activity_in_redis(session_id).await?;

        Ok(())
    }

    /// Mark MFA as verified for a session
    pub async fn mark_mfa_verified(&self, session_id: Uuid) -> Result<(), SessionError> {
        let query = r#"
            UPDATE user_sessions
            SET mfa_verified_at = NOW()
            WHERE id = $1 AND is_active = TRUE
        "#;

        sqlx::query(query)
            .bind(session_id)
            .execute(&self.db_pool)
            .await
            .map_err(|e| SessionError::DatabaseError(e.to_string()))?;

        // Update Redis cache
        self.update_mfa_verification_in_redis(session_id).await?;

        Ok(())
    }

    /// Clean up expired sessions
    pub async fn cleanup_expired_sessions(&self) -> Result<SessionMetrics, SessionError> {
        let start_time = Utc::now();

        // Remove expired sessions from database
        let query = r#"
            UPDATE user_sessions
            SET is_active = FALSE,
                invalidated_at = NOW(),
                invalidated_reason = 'expired'
            WHERE is_active = TRUE AND expires_at < NOW()
        "#;

        let result = sqlx::query(query)
            .execute(&self.db_pool)
            .await
            .map_err(|e| SessionError::DatabaseError(e.to_string()))?;

        let expired_count = result.rows_affected();

        // Clean up Redis cache
        self.cleanup_expired_sessions_from_redis().await?;

        // Generate metrics
        let metrics = self.generate_session_metrics().await?;

        info!("Cleaned up {} expired sessions in {:?}",
              expired_count,
              Utc::now() - start_time);

        Ok(metrics)
    }

    /// Get session metrics
    pub async fn get_session_metrics(&self) -> Result<SessionMetrics, SessionError> {
        self.generate_session_metrics().await
    }

    // Private helper methods

    fn validate_create_session_request(&self, request: &CreateSessionRequest) -> Result<(), SessionError> {
        if request.device_fingerprint.as_ref().map_or(false, |fp| fp.is_empty()) {
            return Err(SessionError::ValidationError("Device fingerprint cannot be empty".to_string()));
        }

        if request.ip_address.as_ref().map_or(false, |ip| ip.is_empty()) {
            return Err(SessionError::ValidationError("IP address cannot be empty".to_string()));
        }

        Ok(())
    }

    async fn check_concurrent_session_limits(&self, user_id: Uuid) -> Result<(), SessionError> {
        let active_sessions = self.get_active_user_sessions(user_id).await?;

        if active_sessions.len() >= self.policy.max_concurrent_sessions as usize {
            return Err(SessionError::MaxSessionsExceeded);
        }

        Ok(())
    }

    fn generate_session_token(&self) -> String {
        use rand::Rng;
        let mut rng = rand::thread_rng();
        let token: String = (0..64)
            .map(|_| {
                let chars = b"abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
                chars[rng.random_range(0..chars.len())] as char
            })
            .collect();
        format!("sess_{}", token)
    }

    fn generate_refresh_token(&self) -> String {
        use rand::Rng;
        let mut rng = rand::thread_rng();
        let token: String = (0..64)
            .map(|_| {
                let chars = b"abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
                chars[rng.random_range(0..chars.len())] as char
            })
            .collect();
        format!("refresh_{}", token)
    }

    fn is_session_valid(&self, session: &UserSession) -> bool {
        session.is_active &&
        session.expires_at > Utc::now() &&
        session.invalidated_at.is_none()
    }

    // Database operations

    async fn store_session_in_db(&self, session: &UserSession) -> Result<(), SessionError> {
        let query = r#"
            INSERT INTO user_sessions (
                id, user_id, session_token, refresh_token,
                device_fingerprint, device_name, ip_address, user_agent,
                location_country, location_city, expires_at, last_activity_at,
                is_active, is_trusted_device, requires_mfa, mfa_verified_at,
                created_at, invalidated_at, invalidated_reason
            ) VALUES (
                $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19
            )
        "#;

        sqlx::query(query)
            .bind(session.id)
            .bind(session.user_id)
            .bind(&session.session_token)
            .bind(&session.refresh_token)
            .bind(&session.device_fingerprint)
            .bind(&session.device_name)
            .bind(&session.ip_address)
            .bind(&session.user_agent)
            .bind(&session.location_country)
            .bind(&session.location_city)
            .bind(session.expires_at)
            .bind(session.last_activity_at)
            .bind(session.is_active)
            .bind(session.is_trusted_device)
            .bind(session.requires_mfa)
            .bind(session.mfa_verified_at)
            .bind(session.created_at)
            .bind(session.invalidated_at)
            .bind(&session.invalidated_reason)
            .execute(&self.db_pool)
            .await
            .map_err(|e| SessionError::DatabaseError(e.to_string()))?;

        Ok(())
    }

    async fn update_session_in_db(&self, session: &UserSession) -> Result<(), SessionError> {
        let query = r#"
            UPDATE user_sessions SET
                session_token = $2,
                refresh_token = $3,
                expires_at = $4,
                last_activity_at = $5,
                mfa_verified_at = $6
            WHERE id = $1
        "#;

        sqlx::query(query)
            .bind(session.id)
            .bind(&session.session_token)
            .bind(&session.refresh_token)
            .bind(session.expires_at)
            .bind(session.last_activity_at)
            .bind(session.mfa_verified_at)
            .execute(&self.db_pool)
            .await
            .map_err(|e| SessionError::DatabaseError(e.to_string()))?;

        Ok(())
    }

    async fn get_session_from_db(&self, session_token: &str) -> Result<UserSession, SessionError> {
        let query = r#"
            SELECT * FROM user_sessions
            WHERE session_token = $1
        "#;

        sqlx::query_as::<_, UserSession>(query)
            .bind(session_token)
            .fetch_one(&self.db_pool)
            .await
            .map_err(|_| SessionError::SessionNotFound)
    }

    async fn get_session_by_refresh_token(&self, refresh_token: &str) -> Result<UserSession, SessionError> {
        let query = r#"
            SELECT * FROM user_sessions
            WHERE refresh_token = $1
        "#;

        sqlx::query_as::<_, UserSession>(query)
            .bind(refresh_token)
            .fetch_one(&self.db_pool)
            .await
            .map_err(|_| SessionError::SessionNotFound)
    }

    async fn get_session_by_id(&self, session_id: Uuid) -> Result<UserSession, SessionError> {
        let query = r#"
            SELECT * FROM user_sessions
            WHERE id = $1
        "#;

        sqlx::query_as::<_, UserSession>(query)
            .bind(session_id)
            .fetch_one(&self.db_pool)
            .await
            .map_err(|_| SessionError::SessionNotFound)
    }

    // Redis operations

    fn get_redis_connection(&self) -> Result<Connection, SessionError> {
        let client = redis::Client::open(self.redis_url.as_str())
            .map_err(|e| SessionError::RedisError(e.to_string()))?;

        client.get_connection()
            .map_err(|e| SessionError::RedisError(e.to_string()))
    }

    async fn store_session_in_redis(&self, session: &UserSession) -> Result<(), SessionError> {
        let mut conn = self.get_redis_connection()?;

        let session_key = format!("session:{}", session.session_token);
        let session_data = serde_json::to_string(session)
            .map_err(|e| SessionError::InternalError(e.to_string()))?;

        let ttl = (session.expires_at - Utc::now()).num_seconds().max(0) as u64;

        let _: () = conn.set_ex(&session_key, session_data, ttl)
            .map_err(|e| SessionError::RedisError(e.to_string()))?;

        // Also store by session ID for quick lookups
        let id_key = format!("session_id:{}", session.id);
        let _: () = conn.set_ex(&id_key, &session.session_token, ttl)
            .map_err(|e| SessionError::RedisError(e.to_string()))?;

        // Track user sessions
        let user_sessions_key = format!("user_sessions:{}", session.user_id);
        let _: () = conn.sadd(&user_sessions_key, session.id.to_string())
            .map_err(|e| SessionError::RedisError(e.to_string()))?;
        let _: () = conn.expire(&user_sessions_key, ttl as i64)
            .map_err(|e| SessionError::RedisError(e.to_string()))?;

        Ok(())
    }

    async fn get_session_from_redis(&self, session_token: &str) -> Result<UserSession, SessionError> {
        let mut conn = self.get_redis_connection()?;

        let session_key = format!("session:{}", session_token);
        let session_data: String = conn.get(&session_key)
            .map_err(|_| SessionError::SessionNotFound)?;

        serde_json::from_str(&session_data)
            .map_err(|e| SessionError::InternalError(e.to_string()))
    }

    async fn remove_session_from_redis(&self, session_token: &str) -> Result<(), SessionError> {
        let mut conn = self.get_redis_connection()?;

        let session_key = format!("session:{}", session_token);
        let _: () = conn.del(&session_key)
            .map_err(|e| SessionError::RedisError(e.to_string()))?;

        Ok(())
    }

    async fn remove_session_from_redis_by_id(&self, session_id: Uuid) -> Result<(), SessionError> {
        let mut conn = self.get_redis_connection()?;

        // Get session token from ID mapping
        let id_key = format!("session_id:{}", session_id);
        if let Ok(session_token) = conn.get::<_, String>(&id_key) {
            let session_key = format!("session:{}", session_token);
            let _: () = conn.del(&session_key)
                .map_err(|e| SessionError::RedisError(e.to_string()))?;
        }

        // Remove ID mapping
        let _: () = conn.del(&id_key)
            .map_err(|e| SessionError::RedisError(e.to_string()))?;

        Ok(())
    }

    async fn update_session_activity_in_redis(&self, session_id: Uuid) -> Result<(), SessionError> {
        let mut conn = self.get_redis_connection()?;

        let id_key = format!("session_id:{}", session_id);
        if let Ok(session_token) = conn.get::<_, String>(&id_key) {
            if let Ok(mut session) = self.get_session_from_redis(&session_token).await {
                session.last_activity_at = Utc::now();
                self.store_session_in_redis(&session).await?;
            }
        }

        Ok(())
    }

    async fn update_mfa_verification_in_redis(&self, session_id: Uuid) -> Result<(), SessionError> {
        let mut conn = self.get_redis_connection()?;

        let id_key = format!("session_id:{}", session_id);
        if let Ok(session_token) = conn.get::<_, String>(&id_key) {
            if let Ok(mut session) = self.get_session_from_redis(&session_token).await {
                session.mfa_verified_at = Some(Utc::now());
                self.store_session_in_redis(&session).await?;
            }
        }

        Ok(())
    }

    async fn cleanup_expired_sessions_from_redis(&self) -> Result<(), SessionError> {
        let mut conn = self.get_redis_connection()?;

        // Redis TTL will automatically clean up expired keys
        // But we can also manually scan and clean up if needed
        let pattern = "session:*";
        let keys: Vec<String> = conn.keys(pattern)
            .map_err(|e| SessionError::RedisError(e.to_string()))?;

        for key in keys {
            if let Ok(session_data) = conn.get::<_, String>(&key) {
                if let Ok(session) = serde_json::from_str::<UserSession>(&session_data) {
                    if !self.is_session_valid(&session) {
                        let _: () = conn.del(&key)
                            .map_err(|e| SessionError::RedisError(e.to_string()))?;
                    }
                }
            }
        }

        Ok(())
    }

    async fn generate_session_metrics(&self) -> Result<SessionMetrics, SessionError> {
        let query = r#"
            SELECT
                COUNT(*) as total_active,
                user_id,
                COUNT(*) as user_session_count
            FROM user_sessions
            WHERE is_active = TRUE AND expires_at > NOW()
            GROUP BY user_id
        "#;

        let rows = sqlx::query(query)
            .fetch_all(&self.db_pool)
            .await
            .map_err(|e| SessionError::DatabaseError(e.to_string()))?;

        let mut sessions_by_user = HashMap::new();
        let mut total_active_sessions = 0u64;

        for row in rows {
            let user_id: Uuid = row.get("user_id");
            let count: i64 = row.get("user_session_count");
            sessions_by_user.insert(user_id, count as u32);
            total_active_sessions += count as u64;
        }

        // Get device metrics
        let device_query = r#"
            SELECT
                device_fingerprint,
                COUNT(*) as device_session_count
            FROM user_sessions
            WHERE is_active = TRUE AND expires_at > NOW() AND device_fingerprint IS NOT NULL
            GROUP BY device_fingerprint
        "#;

        let device_rows = sqlx::query(device_query)
            .fetch_all(&self.db_pool)
            .await
            .map_err(|e| SessionError::DatabaseError(e.to_string()))?;

        let mut sessions_by_device = HashMap::new();
        for row in device_rows {
            let device_fingerprint: String = row.get("device_fingerprint");
            let count: i64 = row.get("device_session_count");
            sessions_by_device.insert(device_fingerprint, count as u32);
        }

        Ok(SessionMetrics {
            total_active_sessions,
            sessions_by_user,
            sessions_by_device,
            expired_sessions_cleaned: 0, // This would be tracked separately
            last_cleanup_at: Utc::now(),
        })
    }

    async fn update_device_tracking(&self, session: &UserSession) -> Result<(), SessionError> {
        if let Some(device_fingerprint) = &session.device_fingerprint {
            let device_key = format!("device:{}", device_fingerprint);
            let mut conn = self.get_redis_connection()?;

            let device_info = DeviceInfo {
                fingerprint: device_fingerprint.clone(),
                name: session.device_name.clone(),
                user_agent: session.user_agent.clone(),
                is_trusted: session.is_trusted_device,
                first_seen: session.created_at,
                last_seen: session.last_activity_at,
                session_count: 1,
            };

            let device_data = serde_json::to_string(&device_info)
                .map_err(|e| SessionError::InternalError(e.to_string()))?;

            let _: () = conn.set_ex(&device_key, device_data, 86400 * 30) // 30 days
                .map_err(|e| SessionError::RedisError(e.to_string()))?;
        }

        Ok(())
    }

    async fn log_session_activity(
        &self,
        session: &UserSession,
        activity_type: &str,
        metadata: Option<serde_json::Value>,
    ) -> Result<(), SessionError> {
        let activity = SessionActivity {
            session_id: session.id,
            user_id: session.user_id,
            activity_type: activity_type.to_string(),
            ip_address: session.ip_address.clone(),
            user_agent: session.user_agent.clone(),
            timestamp: Utc::now(),
            metadata,
        };

        // Log to database
        let query = r#"
            INSERT INTO security_events (
                user_id, event_type, event_category, severity,
                description, metadata, ip_address, user_agent,
                session_id, occurred_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
        "#;

        let description = match activity_type {
            "session_created" => "User session created",
            "session_refreshed" => "User session refreshed",
            "session_invalidated" => "User session invalidated",
            _ => "Session activity",
        };

        sqlx::query(query)
            .bind(activity.user_id)
            .bind(&activity.activity_type)
            .bind("AUTH")
            .bind("LOW")
            .bind(description)
            .bind(&activity.metadata)
            .bind(&activity.ip_address)
            .bind(&activity.user_agent)
            .bind(activity.session_id)
            .bind(activity.timestamp)
            .execute(&self.db_pool)
            .await
            .map_err(|e| SessionError::DatabaseError(e.to_string()))?;

        Ok(())
    }



    /// Invalidate session by device fingerprint
    ///
    /// This method invalidates a specific session based on the device fingerprint.
    /// This is useful for logging out from a specific device.
    ///
    /// # Arguments
    /// * `user_id` - The ID of the user
    /// * `device_fingerprint` - The device fingerprint to invalidate
    ///
    /// # Returns
    /// * `Result<(), SessionError>` - Success or error details
    pub async fn invalidate_session_by_device(&self, user_id: Uuid, device_fingerprint: &str) -> Result<(), SessionError> {
        let now = Utc::now();

        // Invalidate session in database
        let query = r#"
            UPDATE user_sessions
            SET is_active = FALSE,
                invalidated_at = $1,
                invalidated_reason = 'Device logout'
            WHERE user_id = $2 AND device_fingerprint = $3 AND is_active = TRUE
            RETURNING id, session_token
        "#;

        let rows = sqlx::query(query)
            .bind(now)
            .bind(user_id)
            .bind(device_fingerprint)
            .fetch_all(&self.db_pool)
            .await
            .map_err(|e| SessionError::DatabaseError(e.to_string()))?;

        // Remove from Redis cache
        for row in rows {
            let session_id: Uuid = row.get("id");
            let session_token: String = row.get("session_token");

            let _ = self.remove_session_from_redis(&session_token).await;
            let _ = self.remove_session_from_redis_by_id(session_id).await;
        }

        info!("Invalidated session for user: {} device: {}", user_id, device_fingerprint);
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use sqlx::PgPool;

    // Helper function to create test session service
    async fn create_test_service() -> SessionService {
        // Use the actual running database and Redis instance
        let database_url = std::env::var("DATABASE_URL")
            .unwrap_or_else(|_| "postgresql://admin:secret@localhost:5432/crabshield_db".to_string());
        let pool = PgPool::connect(&database_url).await.unwrap();
        SessionService::new(pool, "redis://localhost:6379".to_string())
    }

    #[tokio::test]
    async fn test_session_token_generation() {
        let service = create_test_service().await;
        let token1 = service.generate_session_token();
        let token2 = service.generate_session_token();

        assert_ne!(token1, token2);
        assert!(token1.starts_with("sess_"));
        assert_eq!(token1.len(), 69); // "sess_" + 64 chars
    }

    #[tokio::test]
    async fn test_refresh_token_generation() {
        let service = create_test_service().await;
        let token1 = service.generate_refresh_token();
        let token2 = service.generate_refresh_token();

        assert_ne!(token1, token2);
        assert!(token1.starts_with("refresh_"));
        assert_eq!(token1.len(), 72); // "refresh_" + 64 chars
    }

    #[test]
    fn test_session_validation() {
        // Test session validation logic without database dependency
        // We'll test the is_session_valid method directly

        let valid_session = UserSession {
            id: Uuid::new_v4(),
            user_id: Uuid::new_v4(),
            session_token: "test_token".to_string(),
            refresh_token: "test_refresh".to_string(),
            device_fingerprint: None,
            device_name: None,
            ip_address: None,
            user_agent: None,
            location_country: None,
            location_city: None,
            expires_at: Utc::now() + Duration::hours(1),
            last_activity_at: Utc::now(),
            is_active: true,
            is_trusted_device: false,
            requires_mfa: true,
            mfa_verified_at: None,
            created_at: Utc::now(),
            invalidated_at: None,
            invalidated_reason: None,
        };

        // Test validation logic directly without service instance
        assert!(valid_session.is_active &&
                valid_session.expires_at > Utc::now() &&
                valid_session.invalidated_at.is_none());

        let expired_session = UserSession {
            expires_at: Utc::now() - Duration::hours(1),
            ..valid_session.clone()
        };

        assert!(!(expired_session.is_active &&
                  expired_session.expires_at > Utc::now() &&
                  expired_session.invalidated_at.is_none()));

        let inactive_session = UserSession {
            is_active: false,
            ..valid_session.clone()
        };

        assert!(!(inactive_session.is_active &&
                  inactive_session.expires_at > Utc::now() &&
                  inactive_session.invalidated_at.is_none()));
    }
}
