use anyhow::Result;
use thiserror::Error;
use uuid::Uuid;
use chrono::{Utc, Duration};
use sqlx::PgPool;
use totp_rs::{Algorithm, TOTP, Secret};
use qrcode::{QrCode, render::svg};
use base32;
use base64;
use rand::{thread_rng, Rng};
// use std::collections::HashSet; // For future backup code validation
// use regex::Regex; // For future input validation

use crate::models::{
    UserMfaSecret, TrustedDevice, MfaVerificationAttempt, MfaBackupCode,
    MfaSetupRequest, MfaSetupResponse, MfaVerifySetupRequest, 
    MfaVerifyRequest, MfaVerifyResponse, MfaStatusResponse, GenerateBackupCodesResponse
};

#[derive(Error, Debug)]
pub enum MfaError {
    #[error("MFA setup failed: {0}")]
    SetupFailed(String),
    #[error("MFA verification failed: {0}")]
    VerificationFailed(String),
    #[error("Invalid TOTP code")]
    InvalidTotpCode,
    #[error("Invalid backup code")]
    InvalidBackupCode,
    #[error("MFA not enabled for user")]
    MfaNotEnabled,
    #[error("MFA already enabled for user")]
    MfaAlreadyEnabled,
    #[error("QR code generation failed: {0}")]
    QrCodeFailed(String),
    #[error("Database error: {0}")]
    DatabaseError(String),
    #[error("User not found")]
    UserNotFound,
    #[error("Device not trusted")]
    DeviceNotTrusted,
    #[error("Backup codes exhausted")]
    BackupCodesExhausted,
}

#[derive(Clone)]
pub struct MfaService {
    db_pool: PgPool,
    issuer: String,
    backup_code_length: usize,
    backup_code_count: usize,
}

impl MfaService {
    pub fn new(db_pool: PgPool, issuer: String) -> Self {
        Self {
            db_pool,
            issuer,
            backup_code_length: 8,
            backup_code_count: 10,
        }
    }

    /// Initialize MFA setup for a user
    pub async fn setup_mfa(&self, user_id: Uuid, _request: MfaSetupRequest) -> Result<MfaSetupResponse, MfaError> {
        // Check if MFA is already enabled
        if self.is_mfa_enabled(user_id).await? {
            return Err(MfaError::MfaAlreadyEnabled);
        }

        // Generate secret key
        use rand::Rng;
        let mut rng = rand::thread_rng();
        let secret_bytes: Vec<u8> = (0..20).map(|_| rng.random()).collect();
        let secret = Secret::Raw(secret_bytes);
        let secret_str = secret.to_encoded().to_string();

        // Create TOTP instance
        let totp = TOTP::new(
            Algorithm::SHA1,
            6,
            1,
            30,
            secret.to_bytes().unwrap(),
            Some(self.issuer.clone()),
            format!("user_{}", user_id),
        ).map_err(|e| MfaError::SetupFailed(e.to_string()))?;

        // Generate QR code
        let qr_uri = totp.get_url();
        let qr_code = self.generate_qr_code(&qr_uri)?;

        // Generate backup codes
        let backup_codes = self.generate_backup_codes();

        // Store MFA secret in database (not enabled yet)
        let mfa_secret = UserMfaSecret {
            id: Uuid::new_v4(),
            user_id,
            secret_key: secret_str.clone(),
            is_enabled: false,
            backup_codes: backup_codes.clone(),
            backup_codes_used: vec![],
            algorithm: "SHA1".to_string(),
            digits: 6,
            period: 30,
            recovery_codes_generated_at: Some(Utc::now()),
            last_used_at: None,
            created_at: Utc::now(),
            updated_at: Utc::now(),
            enabled_at: None,
            disabled_at: None,
        };

        self.store_mfa_secret(&mfa_secret).await?;

        Ok(MfaSetupResponse {
            secret: secret_str,
            qr_code,
            backup_codes,
            setup_uri: qr_uri,
        })
    }

    /// Verify MFA setup and enable MFA for the user
    pub async fn verify_setup(&self, user_id: Uuid, request: MfaVerifySetupRequest) -> Result<(), MfaError> {
        // Get the pending MFA secret
        let mfa_secret = self.get_mfa_secret(user_id).await?;
        
        if mfa_secret.is_enabled {
            return Err(MfaError::MfaAlreadyEnabled);
        }

        // Verify the TOTP code
        if !self.verify_totp_code(&mfa_secret.secret_key, &request.totp_code)? {
            return Err(MfaError::InvalidTotpCode);
        }

        // Enable MFA
        self.enable_mfa(user_id).await?;

        Ok(())
    }

    /// Verify MFA code during login
    pub async fn verify_mfa(
        &self, 
        user_id: Uuid, 
        request: MfaVerifyRequest,
        ip_address: Option<std::net::IpAddr>,
        user_agent: Option<String>,
        device_fingerprint: Option<String>,
    ) -> Result<MfaVerifyResponse, MfaError> {
        let mfa_secret = self.get_mfa_secret(user_id).await?;
        
        if !mfa_secret.is_enabled {
            return Err(MfaError::MfaNotEnabled);
        }

        let mut success = false;
        let mut backup_codes_remaining = None;

        match request.verification_type.as_str() {
            "TOTP" => {
                success = self.verify_totp_code(&mfa_secret.secret_key, &request.code)?;
            },
            "BACKUP_CODE" => {
                success = self.verify_backup_code(user_id, &request.code).await?;
                backup_codes_remaining = Some(self.get_remaining_backup_codes_count(user_id).await?);
            },
            _ => return Err(MfaError::VerificationFailed("Invalid verification type".to_string())),
        }

        // Log verification attempt
        self.log_verification_attempt(
            user_id,
            &request.verification_type,
            success,
            ip_address,
            user_agent,
            device_fingerprint.clone(),
        ).await?;

        let mut device_trusted = false;

        // Handle device trust if verification successful
        if success && request.trust_device.unwrap_or(false) {
            if let Some(fingerprint) = device_fingerprint {
                self.trust_device(user_id, fingerprint, ip_address).await?;
                device_trusted = true;
            }
        }

        // Update last used timestamp
        if success {
            self.update_last_used(user_id).await?;
        }

        Ok(MfaVerifyResponse {
            success,
            device_trusted,
            backup_codes_remaining,
        })
    }

    /// Check if device is trusted for the user
    pub async fn is_device_trusted(&self, user_id: Uuid, device_fingerprint: &str) -> Result<bool, MfaError> {
        let query = r#"
            SELECT COUNT(*) as count
            FROM trusted_devices 
            WHERE user_id = $1 
            AND device_fingerprint = $2 
            AND (trusted_until IS NULL OR trusted_until > NOW())
            AND revoked_at IS NULL
        "#;

        let row: (i64,) = sqlx::query_as(query)
            .bind(user_id)
            .bind(device_fingerprint)
            .fetch_one(&self.db_pool)
            .await
            .map_err(|e| MfaError::DatabaseError(e.to_string()))?;

        Ok(row.0 > 0)
    }

    /// Generate new backup codes
    pub async fn generate_new_backup_codes(&self, user_id: Uuid) -> Result<GenerateBackupCodesResponse, MfaError> {
        let mfa_secret = self.get_mfa_secret(user_id).await?;
        
        if !mfa_secret.is_enabled {
            return Err(MfaError::MfaNotEnabled);
        }

        let backup_codes = self.generate_backup_codes();
        let generated_at = Utc::now();

        // Update backup codes in database
        let query = r#"
            UPDATE user_mfa_secrets 
            SET backup_codes = $1, 
                backup_codes_used = '{}',
                recovery_codes_generated_at = $2,
                updated_at = $2
            WHERE user_id = $3
        "#;

        sqlx::query(query)
            .bind(&backup_codes)
            .bind(generated_at)
            .bind(user_id)
            .execute(&self.db_pool)
            .await
            .map_err(|e| MfaError::DatabaseError(e.to_string()))?;

        Ok(GenerateBackupCodesResponse {
            backup_codes,
            generated_at,
        })
    }

    /// Get MFA status for a user
    pub async fn get_mfa_status(&self, user_id: Uuid) -> Result<MfaStatusResponse, MfaError> {
        let mfa_secret = match self.get_mfa_secret(user_id).await {
            Ok(secret) => Some(secret),
            Err(MfaError::UserNotFound) => None,
            Err(e) => return Err(e),
        };

        let is_enabled = mfa_secret.as_ref().map(|s| s.is_enabled).unwrap_or(false);
        let backup_codes_count = if let Some(ref secret) = mfa_secret {
            (secret.backup_codes.len() - secret.backup_codes_used.len()) as i32
        } else {
            0
        };
        let last_used_at = mfa_secret.and_then(|s| s.last_used_at);

        let trusted_devices_count = self.get_trusted_devices_count(user_id).await?;

        Ok(MfaStatusResponse {
            is_enabled,
            backup_codes_count,
            last_used_at,
            trusted_devices_count,
        })
    }

    /// Disable MFA for a user
    pub async fn disable_mfa(&self, user_id: Uuid) -> Result<(), MfaError> {
        let query = r#"
            UPDATE user_mfa_secrets 
            SET is_enabled = false, 
                disabled_at = NOW(),
                updated_at = NOW()
            WHERE user_id = $1
        "#;

        sqlx::query(query)
            .bind(user_id)
            .execute(&self.db_pool)
            .await
            .map_err(|e| MfaError::DatabaseError(e.to_string()))?;

        // Revoke all trusted devices
        self.revoke_all_trusted_devices(user_id).await?;

        Ok(())
    }

    /// Check if MFA is enabled for a user
    pub async fn is_mfa_enabled(&self, user_id: Uuid) -> Result<bool, MfaError> {
        let query = r#"
            SELECT is_enabled 
            FROM user_mfa_secrets 
            WHERE user_id = $1
        "#;

        let row: Option<(bool,)> = sqlx::query_as(query)
            .bind(user_id)
            .fetch_optional(&self.db_pool)
            .await
            .map_err(|e| MfaError::DatabaseError(e.to_string()))?;

        Ok(row.map(|r| r.0).unwrap_or(false))
    }

    // Private helper methods

    fn generate_qr_code(&self, uri: &str) -> Result<String, MfaError> {
        let qr_code = QrCode::new(uri)
            .map_err(|e| MfaError::QrCodeFailed(e.to_string()))?;
        
        let svg = qr_code
            .render::<svg::Color>()
            .min_dimensions(200, 200)
            .build();

        // Convert SVG to base64
        use base64::{Engine as _, engine::general_purpose};
        let encoded = general_purpose::STANDARD.encode(svg.as_bytes());
        Ok(format!("data:image/svg+xml;base64,{}", encoded))
    }

    fn generate_backup_codes(&self) -> Vec<String> {
        let mut codes = Vec::new();
        let mut rng = thread_rng();
        
        for _ in 0..self.backup_code_count {
            let code: String = (0..self.backup_code_length)
                .map(|_| rng.random_range(0..10).to_string())
                .collect();
            codes.push(code);
        }
        
        codes
    }

    fn verify_totp_code(&self, secret: &str, code: &str) -> Result<bool, MfaError> {
        let secret_bytes = base32::decode(base32::Alphabet::Rfc4648 { padding: true }, secret)
            .ok_or_else(|| MfaError::VerificationFailed("Invalid secret format".to_string()))?;

        let totp = TOTP::new(
            Algorithm::SHA1,
            6,
            1,
            30,
            secret_bytes,
            Some(self.issuer.clone()),
            "user".to_string(),
        ).map_err(|e| MfaError::VerificationFailed(e.to_string()))?;

        Ok(totp.check_current(code).unwrap_or(false))
    }

    async fn verify_backup_code(&self, user_id: Uuid, code: &str) -> Result<bool, MfaError> {
        let mfa_secret = self.get_mfa_secret(user_id).await?;

        // Check if code exists in backup codes and hasn't been used
        if !mfa_secret.backup_codes.contains(&code.to_string()) {
            return Ok(false);
        }

        if mfa_secret.backup_codes_used.contains(&code.to_string()) {
            return Ok(false);
        }

        // Mark backup code as used
        let mut used_codes = mfa_secret.backup_codes_used.clone();
        used_codes.push(code.to_string());

        let query = r#"
            UPDATE user_mfa_secrets
            SET backup_codes_used = $1,
                updated_at = NOW()
            WHERE user_id = $2
        "#;

        sqlx::query(query)
            .bind(&used_codes)
            .bind(user_id)
            .execute(&self.db_pool)
            .await
            .map_err(|e| MfaError::DatabaseError(e.to_string()))?;

        Ok(true)
    }

    async fn get_mfa_secret(&self, user_id: Uuid) -> Result<UserMfaSecret, MfaError> {
        let query = r#"
            SELECT id, user_id, secret_key, is_enabled, backup_codes, backup_codes_used,
                   algorithm, digits, period, recovery_codes_generated_at, last_used_at,
                   created_at, updated_at, enabled_at, disabled_at
            FROM user_mfa_secrets
            WHERE user_id = $1
        "#;

        sqlx::query_as::<_, UserMfaSecret>(query)
            .bind(user_id)
            .fetch_one(&self.db_pool)
            .await
            .map_err(|e| match e {
                sqlx::Error::RowNotFound => MfaError::UserNotFound,
                _ => MfaError::DatabaseError(e.to_string()),
            })
    }

    async fn store_mfa_secret(&self, mfa_secret: &UserMfaSecret) -> Result<(), MfaError> {
        let query = r#"
            INSERT INTO user_mfa_secrets (
                id, user_id, secret_key, is_enabled, backup_codes, backup_codes_used,
                algorithm, digits, period, recovery_codes_generated_at, last_used_at,
                created_at, updated_at, enabled_at, disabled_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)
        "#;

        sqlx::query(query)
            .bind(mfa_secret.id)
            .bind(mfa_secret.user_id)
            .bind(&mfa_secret.secret_key)
            .bind(mfa_secret.is_enabled)
            .bind(&mfa_secret.backup_codes)
            .bind(&mfa_secret.backup_codes_used)
            .bind(&mfa_secret.algorithm)
            .bind(mfa_secret.digits)
            .bind(mfa_secret.period)
            .bind(mfa_secret.recovery_codes_generated_at)
            .bind(mfa_secret.last_used_at)
            .bind(mfa_secret.created_at)
            .bind(mfa_secret.updated_at)
            .bind(mfa_secret.enabled_at)
            .bind(mfa_secret.disabled_at)
            .execute(&self.db_pool)
            .await
            .map_err(|e| MfaError::DatabaseError(e.to_string()))?;

        Ok(())
    }

    async fn enable_mfa(&self, user_id: Uuid) -> Result<(), MfaError> {
        let query = r#"
            UPDATE user_mfa_secrets
            SET is_enabled = true,
                enabled_at = NOW(),
                updated_at = NOW()
            WHERE user_id = $1
        "#;

        sqlx::query(query)
            .bind(user_id)
            .execute(&self.db_pool)
            .await
            .map_err(|e| MfaError::DatabaseError(e.to_string()))?;

        Ok(())
    }

    async fn trust_device(
        &self,
        user_id: Uuid,
        device_fingerprint: String,
        ip_address: Option<std::net::IpAddr>
    ) -> Result<(), MfaError> {
        let trusted_device = TrustedDevice {
            id: Uuid::new_v4(),
            user_id,
            device_fingerprint,
            device_name: None,
            device_type: None,
            trust_level: "FULL".to_string(),
            trusted_until: Some(Utc::now() + Duration::days(30)), // Trust for 30 days
            browser_name: None,
            browser_version: None,
            os_name: None,
            os_version: None,
            ip_address: ip_address.map(|ip| ip.to_string()),
            country_code: None,
            city: None,
            last_used_at: Utc::now(),
            usage_count: 1,
            created_at: Utc::now(),
            expires_at: Some(Utc::now() + Duration::days(30)),
            revoked_at: None,
            revoked_reason: None,
        };

        let query = r#"
            INSERT INTO trusted_devices (
                id, user_id, device_fingerprint, device_name, device_type,
                trust_level, trusted_until, browser_name, browser_version,
                os_name, os_version, ip_address, country_code, city,
                last_used_at, usage_count, created_at, expires_at,
                revoked_at, revoked_reason
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20)
            ON CONFLICT (user_id, device_fingerprint)
            DO UPDATE SET
                last_used_at = EXCLUDED.last_used_at,
                usage_count = trusted_devices.usage_count + 1,
                trusted_until = EXCLUDED.trusted_until,
                expires_at = EXCLUDED.expires_at
        "#;

        sqlx::query(query)
            .bind(trusted_device.id)
            .bind(trusted_device.user_id)
            .bind(&trusted_device.device_fingerprint)
            .bind(trusted_device.device_name)
            .bind(trusted_device.device_type)
            .bind(&trusted_device.trust_level)
            .bind(trusted_device.trusted_until)
            .bind(trusted_device.browser_name)
            .bind(trusted_device.browser_version)
            .bind(trusted_device.os_name)
            .bind(trusted_device.os_version)
            .bind(&trusted_device.ip_address)
            .bind(trusted_device.country_code)
            .bind(trusted_device.city)
            .bind(trusted_device.last_used_at)
            .bind(trusted_device.usage_count)
            .bind(trusted_device.created_at)
            .bind(trusted_device.expires_at)
            .bind(trusted_device.revoked_at)
            .bind(trusted_device.revoked_reason)
            .execute(&self.db_pool)
            .await
            .map_err(|e| MfaError::DatabaseError(e.to_string()))?;

        Ok(())
    }

    async fn log_verification_attempt(
        &self,
        user_id: Uuid,
        verification_type: &str,
        success: bool,
        ip_address: Option<std::net::IpAddr>,
        user_agent: Option<String>,
        device_fingerprint: Option<String>,
    ) -> Result<(), MfaError> {
        let attempt = MfaVerificationAttempt {
            id: Uuid::new_v4(),
            user_id,
            session_id: None,
            verification_type: verification_type.to_string(),
            success,
            failure_reason: if success { None } else { Some("Invalid code".to_string()) },
            ip_address: ip_address.map(|ip| ip.to_string()),
            user_agent,
            device_fingerprint,
            attempted_at: Utc::now(),
            verified_at: if success { Some(Utc::now()) } else { None },
        };

        let query = r#"
            INSERT INTO mfa_verification_attempts (
                id, user_id, session_id, verification_type, success, failure_reason,
                ip_address, user_agent, device_fingerprint, attempted_at, verified_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
        "#;

        sqlx::query(query)
            .bind(attempt.id)
            .bind(attempt.user_id)
            .bind(attempt.session_id)
            .bind(&attempt.verification_type)
            .bind(attempt.success)
            .bind(attempt.failure_reason)
            .bind(&attempt.ip_address)
            .bind(attempt.user_agent)
            .bind(attempt.device_fingerprint)
            .bind(attempt.attempted_at)
            .bind(attempt.verified_at)
            .execute(&self.db_pool)
            .await
            .map_err(|e| MfaError::DatabaseError(e.to_string()))?;

        Ok(())
    }

    async fn update_last_used(&self, user_id: Uuid) -> Result<(), MfaError> {
        let query = r#"
            UPDATE user_mfa_secrets
            SET last_used_at = NOW(),
                updated_at = NOW()
            WHERE user_id = $1
        "#;

        sqlx::query(query)
            .bind(user_id)
            .execute(&self.db_pool)
            .await
            .map_err(|e| MfaError::DatabaseError(e.to_string()))?;

        Ok(())
    }

    async fn get_remaining_backup_codes_count(&self, user_id: Uuid) -> Result<i32, MfaError> {
        let mfa_secret = self.get_mfa_secret(user_id).await?;
        let remaining = mfa_secret.backup_codes.len() - mfa_secret.backup_codes_used.len();
        Ok(remaining as i32)
    }

    async fn get_trusted_devices_count(&self, user_id: Uuid) -> Result<i32, MfaError> {
        let query = r#"
            SELECT COUNT(*) as count
            FROM trusted_devices
            WHERE user_id = $1
            AND (trusted_until IS NULL OR trusted_until > NOW())
            AND revoked_at IS NULL
        "#;

        let row: (i64,) = sqlx::query_as(query)
            .bind(user_id)
            .fetch_one(&self.db_pool)
            .await
            .map_err(|e| MfaError::DatabaseError(e.to_string()))?;

        Ok(row.0 as i32)
    }

    async fn revoke_all_trusted_devices(&self, user_id: Uuid) -> Result<(), MfaError> {
        let query = r#"
            UPDATE trusted_devices
            SET revoked_at = NOW(),
                revoked_reason = 'MFA disabled'
            WHERE user_id = $1
            AND revoked_at IS NULL
        "#;

        sqlx::query(query)
            .bind(user_id)
            .execute(&self.db_pool)
            .await
            .map_err(|e| MfaError::DatabaseError(e.to_string()))?;

        Ok(())
    }

    /// Get list of trusted devices for a user
    pub async fn get_trusted_devices(&self, user_id: Uuid) -> Result<Vec<TrustedDevice>, MfaError> {
        let query = r#"
            SELECT id, user_id, device_fingerprint, device_name, device_type,
                   trust_level, trusted_until, browser_name, browser_version,
                   os_name, os_version, ip_address, country_code, city,
                   last_used_at, usage_count, created_at, expires_at,
                   revoked_at, revoked_reason
            FROM trusted_devices
            WHERE user_id = $1
            AND (trusted_until IS NULL OR trusted_until > NOW())
            AND revoked_at IS NULL
            ORDER BY last_used_at DESC
        "#;

        let devices = sqlx::query_as::<_, TrustedDevice>(query)
            .bind(user_id)
            .fetch_all(&self.db_pool)
            .await
            .map_err(|e| MfaError::DatabaseError(e.to_string()))?;

        Ok(devices)
    }

    /// Revoke a specific trusted device
    pub async fn revoke_trusted_device(&self, user_id: Uuid, device_id: Uuid) -> Result<(), MfaError> {
        let query = r#"
            UPDATE trusted_devices
            SET revoked_at = NOW(),
                revoked_reason = 'User revoked'
            WHERE id = $1
            AND user_id = $2
            AND revoked_at IS NULL
        "#;

        let result = sqlx::query(query)
            .bind(device_id)
            .bind(user_id)
            .execute(&self.db_pool)
            .await
            .map_err(|e| MfaError::DatabaseError(e.to_string()))?;

        if result.rows_affected() == 0 {
            return Err(MfaError::DeviceNotTrusted);
        }

        Ok(())
    }
}
