use jsonwebtoken::{decode, encode, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Val<PERSON><PERSON>};
use serde::{Deserialize, Serialize};
use chrono::{Duration, Utc};
use uuid::Uuid;
use anyhow::Result;
use thiserror::Error;
use std::collections::HashSet;

#[derive(Error, Debug)]
pub enum JwtError {
    #[error("Token creation failed: {0}")]
    CreationFailed(String),
    #[error("Token validation failed: {0}")]
    ValidationFailed(String),
    #[error("Token expired")]
    TokenExpired,
    #[error("Token revoked")]
    TokenRevoked,
    #[error("Invalid token format")]
    InvalidFormat,
    #[error("Missing claims: {0}")]
    MissingClaims(String),
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Claims {
    // Standard JWT claims
    pub sub: String,        // Subject (user ID)
    pub iss: String,        // Issuer
    pub aud: String,        // Audience
    pub exp: i64,           // Expiration time
    pub iat: i64,           // Issued at
    pub nbf: i64,           // Not before
    pub jti: String,        // JWT ID
    
    // Custom claims
    pub user_id: Uuid,
    pub email: String,
    pub roles: Vec<String>,
    pub permissions: Vec<String>,
    pub session_id: Uuid,
    pub token_type: TokenType,
    pub mfa_verified: bool,
    pub device_id: Option<String>,
    
    // Security context
    pub ip_address: Option<String>,
    pub user_agent: Option<String>,
    pub trusted_device: bool,
}

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq, Eq)]
pub enum TokenType {
    Access,
    Refresh,
    EmailVerification,
    PasswordReset,
}

#[derive(Debug, Clone)]
pub struct TokenPair {
    pub access_token: String,
    pub refresh_token: String,
    pub access_expires_at: chrono::DateTime<Utc>,
    pub refresh_expires_at: chrono::DateTime<Utc>,
}

#[derive(Clone)]
pub struct JwtService {
    encoding_key: EncodingKey,
    decoding_key: DecodingKey,
    issuer: String,
    audience: String,
    access_token_duration: Duration,
    refresh_token_duration: Duration,
    revoked_tokens: HashSet<String>, // In production, use Redis
}

impl JwtService {
    #[must_use] pub fn new(secret: &str, issuer: String, audience: String) -> Self {
        let encoding_key = EncodingKey::from_secret(secret.as_ref());
        let decoding_key = DecodingKey::from_secret(secret.as_ref());
        
        Self {
            encoding_key,
            decoding_key,
            issuer,
            audience,
            access_token_duration: Duration::minutes(15),  // Short-lived access tokens
            refresh_token_duration: Duration::days(7),     // Longer-lived refresh tokens
            revoked_tokens: HashSet::new(),
        }
    }

    /// Create a new token pair (access + refresh)
    ///
    /// # Errors
    /// Returns `JwtError` if encoding the token fails or required claims are missing.
    ///
    /// # Note
    /// This function takes many arguments (11), which may trigger clippy's [`too_many_arguments`] lint. Refactoring to use a struct is recommended for larger codebase changes.
    pub fn create_token_pair(
        &self,
        user_id: Uuid,
        email: String,
        roles: Vec<String>,
        permissions: Vec<String>,
        session_id: Uuid,
        mfa_verified: bool,
        device_id: Option<String>,
        ip_address: Option<String>,
        user_agent: Option<String>,
        trusted_device: bool,
    ) -> Result<TokenPair, JwtError> {
        let now = Utc::now();
        let access_expires_at = now + self.access_token_duration;
        let refresh_expires_at = now + self.refresh_token_duration;

        // Create access token
        let access_claims = Claims {
            sub: user_id.to_string(),
            iss: self.issuer.clone(),
            aud: self.audience.clone(),
            exp: access_expires_at.timestamp(),
            iat: now.timestamp(),
            nbf: now.timestamp(),
            jti: Uuid::new_v4().to_string(),
            user_id,
            email: email.clone(),
            roles: roles.clone(),
            permissions,
            session_id,
            token_type: TokenType::Access,
            mfa_verified,
            device_id: device_id.clone(),
            ip_address: ip_address.clone(),
            user_agent: user_agent.clone(),
            trusted_device,
        };

        // Create refresh token
        let refresh_claims = Claims {
            sub: user_id.to_string(),
            iss: self.issuer.clone(),
            aud: self.audience.clone(),
            exp: refresh_expires_at.timestamp(),
            iat: now.timestamp(),
            nbf: now.timestamp(),
            jti: Uuid::new_v4().to_string(),
            user_id,
            email,
            roles,
            permissions: vec![], // Refresh tokens don't need permissions
            session_id,
            token_type: TokenType::Refresh,
            mfa_verified,
            device_id,
            ip_address,
            user_agent,
            trusted_device,
        };

        let access_token = self.encode_token(&access_claims)?;
        let refresh_token = self.encode_token(&refresh_claims)?;

        Ok(TokenPair {
            access_token,
            refresh_token,
            access_expires_at,
            refresh_expires_at,
        })
    }

    /// Create a special purpose token (email verification, password reset)
    ///
    /// # Errors
    /// Returns `JwtError` if encoding the token fails or required claims are missing.
    pub fn create_special_token(
        &self,
        user_id: Uuid,
        email: String,
        token_type: TokenType,
        duration: Duration,
    ) -> Result<String, JwtError> {
        let now = Utc::now();
        let expires_at = now + duration;

        let claims = Claims {
            sub: user_id.to_string(),
            iss: self.issuer.clone(),
            aud: self.audience.clone(),
            exp: expires_at.timestamp(),
            iat: now.timestamp(),
            nbf: now.timestamp(),
            jti: Uuid::new_v4().to_string(),
            user_id,
            email,
            roles: vec![],
            permissions: vec![],
            session_id: Uuid::new_v4(), // Dummy session ID for special tokens
            token_type,
            mfa_verified: false,
            device_id: None,
            ip_address: None,
            user_agent: None,
            trusted_device: false,
        };

        self.encode_token(&claims)
    }

    /// Encode claims into a JWT token
    ///
    /// # Errors
    /// Returns `JwtError` if encoding fails.
    fn encode_token(&self, claims: &Claims) -> Result<String, JwtError> {
        let header = Header::new(Algorithm::HS256);
        
        encode(&header, claims, &self.encoding_key)
            .map_err(|e| JwtError::CreationFailed(e.to_string()))
    }

    /// Validate and decode a JWT token
    ///
    /// # Errors
    /// Returns `JwtError` if decoding or validation fails.
    pub fn validate_token(&self, token: &str) -> Result<Claims, JwtError> {
        // Check if token is revoked
        if self.is_token_revoked(token) {
            return Err(JwtError::TokenRevoked);
        }

        let mut validation = Validation::new(Algorithm::HS256);
        validation.set_issuer(&[&self.issuer]);
        validation.set_audience(&[&self.audience]);

        let token_data = decode::<Claims>(token, &self.decoding_key, &validation)
            .map_err(|e| match e.kind() {
                jsonwebtoken::errors::ErrorKind::ExpiredSignature => JwtError::TokenExpired,
                _ => JwtError::ValidationFailed(e.to_string()),
            })?;

        Ok(token_data.claims)
    }

    /// Refresh an access token using a refresh token
    ///
    /// # Errors
    /// Returns `JwtError` if the refresh token is invalid or expired.
    pub fn refresh_access_token(&self, refresh_token: &str) -> Result<String, JwtError> {
        let claims = self.validate_token(refresh_token)?;

        // Verify this is a refresh token
        if claims.token_type != TokenType::Refresh {
            return Err(JwtError::ValidationFailed(
                "Invalid token type for refresh".to_string()
            ));
        }

        // Create new access token with updated expiration
        let now = Utc::now();
        let access_expires_at = now + self.access_token_duration;

        let new_claims = Claims {
            exp: access_expires_at.timestamp(),
            iat: now.timestamp(),
            jti: Uuid::new_v4().to_string(),
            token_type: TokenType::Access,
            ..claims
        };

        self.encode_token(&new_claims)
    }

    /// Revoke a token (add to revocation list)
    ///
    /// # Errors
    /// Returns `JwtError` if the token cannot be decoded or added to the revocation list.
    pub fn revoke_token(&mut self, token: &str) -> Result<(), JwtError> {
        let claims = self.validate_token(token)?;
        self.revoked_tokens.insert(claims.jti);
        Ok(())
    }

    /// Check if a token is revoked
    #[must_use] pub fn is_token_revoked(&self, token: &str) -> bool {
        // Extract JTI from token without full validation
        if let Ok(claims) = self.decode_token_unsafe(token) {
            return self.revoked_tokens.contains(&claims.jti);
        }
        false
    }

    /// Decode token without validation (for revocation checking)
    ///
    /// # Errors
    /// Returns `JwtError` if decoding fails.
    fn decode_token_unsafe(&self, token: &str) -> Result<Claims, JwtError> {
        let mut validation = Validation::new(Algorithm::HS256);
        validation.validate_exp = false;
        validation.validate_nbf = false;
        validation.insecure_disable_signature_validation();

        let token_data = decode::<Claims>(token, &self.decoding_key, &validation)
            .map_err(|e| JwtError::ValidationFailed(e.to_string()))?;

        Ok(token_data.claims)
    }

    /// Extract user ID from token without full validation
    ///
    /// # Errors
    /// Returns `JwtError` if the user ID cannot be extracted.
    pub fn extract_user_id(&self, token: &str) -> Result<Uuid, JwtError> {
        let claims = self.decode_token_unsafe(token)?;
        Ok(claims.user_id)
    }

    /// Validate token and check specific permission
    ///
    /// # Errors
    /// Returns `JwtError` if validation fails or the permission is missing.
    pub fn validate_permission(&self, token: &str, required_permission: &str) -> Result<Claims, JwtError> {
        let claims = self.validate_token(token)?;
        
        if !claims.permissions.contains(&required_permission.to_string()) {
            return Err(JwtError::ValidationFailed(
                format!("Missing required permission: {required_permission}")
            ));
        }

        Ok(claims)
    }

    /// Validate token and check if user has any of the required roles
    ///
    /// # Errors
    /// Returns `JwtError` if validation fails or none of the required roles are present.
    pub fn validate_role(&self, token: &str, required_roles: &[&str]) -> Result<Claims, JwtError> {
        let claims = self.validate_token(token)?;
        
        let has_role = required_roles.iter()
            .any(|role| claims.roles.contains(&(*role).to_string()));

        if !has_role {
            return Err(JwtError::ValidationFailed(
                format!("Missing required role. Required one of: {required_roles:?}")
            ));
        }

        Ok(claims)
    }

    /// Get token expiration time
    ///
    /// # Errors
    /// Returns `JwtError` if the expiration cannot be determined.
    pub fn get_token_expiration(&self, token: &str) -> Result<chrono::DateTime<Utc>, JwtError> {
        let claims = self.decode_token_unsafe(token)?;
        chrono::DateTime::from_timestamp(claims.exp, 0)
            .ok_or(JwtError::InvalidFormat)
    }

    /// Check if token expires soon (within threshold)
    ///
    /// # Errors
    /// Returns `JwtError` if token expiration cannot be determined.
    pub fn expires_soon(&self, token: &str, threshold: Duration) -> Result<bool, JwtError> {
        let expiration = self.get_token_expiration(token)?;
        let now = Utc::now();
        Ok(expiration - now < threshold)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    fn create_test_service() -> JwtService {
        JwtService::new(
            "test_secret_key_for_testing_only",
            "crabshield-auth".to_string(),
            "crabshield-api".to_string(),
        )
    }

    fn create_test_token_pair(service: &JwtService, user_id: Uuid, session_id: Uuid) -> Result<TokenPair, JwtError> {
        service.create_token_pair(
            user_id,
            "<EMAIL>".to_string(),
            vec!["user".to_string()],
            vec!["profile.read".to_string()],
            session_id,
            true,
            None,
            None,
            None,
            false,
        )
    }

    #[test]
    fn test_token_creation_and_validation() -> Result<(), JwtError> {
        let service = create_test_service();
        let user_id = Uuid::new_v4();
        let session_id = Uuid::new_v4();

        let token_pair = create_test_token_pair(&service, user_id, session_id)?;

        // Validate access token
        let claims = service.validate_token(&token_pair.access_token)?;
        assert_eq!(claims.user_id, user_id);
        assert_eq!(claims.token_type, TokenType::Access);
        assert!(claims.mfa_verified);

        // Validate refresh token
        let refresh_claims = service.validate_token(&token_pair.refresh_token)?;
        assert_eq!(refresh_claims.user_id, user_id);
        assert_eq!(refresh_claims.token_type, TokenType::Refresh);
        Ok(())
    }

    #[test]
    fn test_token_refresh() -> Result<(), JwtError> {
        let service = create_test_service();
        let user_id = Uuid::new_v4();
        let session_id = Uuid::new_v4();

        let token_pair = create_test_token_pair(&service, user_id, session_id)?;

        // Refresh access token
        let new_access_token = service.refresh_access_token(&token_pair.refresh_token)?;
        let new_claims = service.validate_token(&new_access_token)?;
        assert_eq!(new_claims.user_id, user_id);
        assert_eq!(new_claims.token_type, TokenType::Access);
        Ok(())
    }

    fn create_test_token_pair_with_permissions(service: &JwtService, user_id: Uuid, session_id: Uuid) -> Result<TokenPair, JwtError> {
        service.create_token_pair(
            user_id,
            "<EMAIL>".to_string(),
            vec!["user".to_string()],
            vec!["profile.read".to_string(), "profile.update".to_string()],
            session_id,
            true,
            None,
            None,
            None,
            false,
        )
    }

    #[test]
    fn test_permission_validation() -> Result<(), JwtError> {
        let service = create_test_service();
        let user_id = Uuid::new_v4();
        let session_id = Uuid::new_v4();

        let token_pair = create_test_token_pair_with_permissions(&service, user_id, session_id)?;

        // Should succeed
        assert!(service.validate_permission(&token_pair.access_token, "profile.read").is_ok());
        // Should fail
        assert!(service.validate_permission(&token_pair.access_token, "admin.users").is_err());
        Ok(())
    }
}
