use crate::models::{User, CreateUserRequest, LoginRequest, LoginAttemptRequest, MfaVerifyRequest};
use crate::services::{PasswordService, LockoutService, MfaService, JwtService};
use crate::utils::errors::AppError;
use anyhow::Result;
use serde::{Serialize, Deserialize};
use sqlx::PgPool;
use uuid::Uuid;
use chrono::{Utc, Duration};
use tracing::{info, warn, error};
use std::net::IpAddr;
use base64::{Engine as _, engine::general_purpose};
use redis::{Client as RedisClient, Commands, Connection};
use jsonwebtoken;

#[derive(Debug, Serialize, Deserialize)]
pub struct AuthTokens {
    pub access_token: String,
    pub refresh_token: String,
    pub expires_in: i64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct MfaVerification {
    pub verified: bool,
    pub recovery_codes: Option<Vec<String>>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AuthLoginResponse {
    pub user: User,
    pub tokens: Option<AuthTokens>,
    pub mfa_required: bool,
    pub mfa_token: Option<String>,
}

#[derive(Clone)]
pub struct AuthService {
    pool: PgPool,
    password_service: PasswordService,
    lockout_service: LockoutService,
    mfa_service: MfaService,
    jwt_service: JwtService,
    redis_client: RedisClient,
}

impl AuthService {
    #[must_use] pub fn new(
        pool: PgPool,
        password_service: PasswordService,
        lockout_service: LockoutService,
        mfa_service: MfaService,
        jwt_service: JwtService,
        redis_client: RedisClient,
    ) -> Self {
        Self {
            pool,
            password_service,
            lockout_service,
            mfa_service,
            jwt_service,
            redis_client,
        }
    }

    /// Create a new user account
    pub async fn create_user(&self, request: CreateUserRequest) -> Result<User, AppError> {
        info!("Creating new user with email: {}", request.email);

        // Check if user already exists
        if self.user_exists_by_email(&request.email).await? {
            warn!("Attempted to create user with existing email: {}", request.email);
            return Err(AppError::BadRequest("User with this email already exists".to_string()));
        }

        // Check if username already exists
        if self.user_exists_by_username(&request.username).await? {
            warn!("Attempted to create user with existing username: {}", request.username);
            return Err(AppError::BadRequest("User with this username already exists".to_string()));
        }

        // Validate password strength
        let password_analysis = self.password_service.analyze_password_strength(&request.password);
        if password_analysis.score < 60 {
            let mut issues = Vec::new();
            if !password_analysis.has_lowercase { issues.push("missing lowercase letters"); }
            if !password_analysis.has_uppercase { issues.push("missing uppercase letters"); }
            if !password_analysis.has_numbers { issues.push("missing numbers"); }
            if !password_analysis.has_symbols { issues.push("missing symbols"); }
            if password_analysis.is_common { issues.push("password is too common"); }
            if password_analysis.length < 8 { issues.push("password is too short"); }

            return Err(AppError::BadRequest(format!(
                "Password is too weak. Score: {}/100. Issues: {}",
                password_analysis.score,
                if issues.is_empty() { "low entropy".to_string() } else { issues.join(", ") }
            )));
        }

        // Hash the password
        let password_hash = self.password_service.hash_password(&request.password)
            .map_err(|e| {
                error!("Failed to hash password: {}", e);
                AppError::InternalServerError("Failed to process password".to_string())
            })?;

        // Create user in database
        let user_id = Uuid::new_v4();
        let now = Utc::now();

        let query = r"
            INSERT INTO users (
                id, email, username, password_hash, first_name, last_name,
                is_active, is_verified, email_verified_at, failed_login_attempts,
                locked_until, last_login_at, last_login_ip, password_changed_at,
                must_change_password, created_at, updated_at, created_by, updated_by
            ) VALUES (
                $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19
            ) RETURNING *
        ";

        let user = sqlx::query_as::<_, User>(query)
            .bind(user_id)
            .bind(&request.email)
            .bind(&request.username)
            .bind(&password_hash)
            .bind(&request.first_name)
            .bind(&request.last_name)
            .bind(true) // is_active
            .bind(false) // is_verified
            .bind(None::<chrono::DateTime<Utc>>) // email_verified_at
            .bind(0) // failed_login_attempts
            .bind(None::<chrono::DateTime<Utc>>) // locked_until
            .bind(None::<chrono::DateTime<Utc>>) // last_login_at
            .bind(None::<String>) // last_login_ip
            .bind(now) // password_changed_at
            .bind(false) // must_change_password
            .bind(now) // created_at
            .bind(now) // updated_at
            .bind(None::<Uuid>) // created_by
            .bind(None::<Uuid>) // updated_by
            .fetch_one(&self.pool)
            .await
            .map_err(|e| {
                error!("Failed to create user in database: {}", e);
                AppError::InternalServerError("Failed to create user".to_string())
            })?;

        info!("Successfully created user with ID: {}", user.id);
        Ok(user)
    }

    /// Authenticate a user with email/username and password
    /// Returns AuthLoginResponse indicating if MFA is required or providing tokens
    pub async fn authenticate_user(&self, request: LoginRequest, ip_address: &str) -> Result<AuthLoginResponse, AppError> {
        info!("Authentication attempt for email: {}", request.email);

        // Check IP rate limiting first
        let ip_rate_limit = self.lockout_service.check_ip_rate_limit(ip_address).await
            .map_err(|e| {
                error!("Failed to check IP rate limit: {}", e);
                AppError::InternalServerError("Authentication service unavailable".to_string())
            })?;

        if ip_rate_limit.is_rate_limited {
            warn!("IP {} is rate limited", ip_address);
            self.log_failed_attempt(&request.email, None, ip_address, "IP rate limited", false).await?;
            return Err(AppError::Unauthorized("Too many requests from this IP address".to_string()));
        }

        // Get user by email
        let Some(user) = self.get_user_by_email(&request.email).await? else {
                warn!("Authentication failed: user not found for email {}", request.email);
                self.log_failed_attempt(&request.email, None, ip_address, "User not found", false).await?;
                return Err(AppError::Unauthorized("Invalid credentials".to_string()));
            };

        // Check if user is active
        if !user.is_active {
            warn!("Authentication failed: user {} is inactive", user.id);
            self.log_failed_attempt(&request.email, Some(user.id), ip_address, "User inactive", false).await?;
            return Err(AppError::Unauthorized("Account is disabled".to_string()));
        }

        // Check account lockout
        let lockout_info = self.lockout_service.check_account_lockout(user.id).await
            .map_err(|e| {
                error!("Failed to check account lockout: {}", e);
                AppError::InternalServerError("Authentication service unavailable".to_string())
            })?;

        if lockout_info.is_locked {
            warn!("Authentication failed: user {} is locked until {:?}", user.id, lockout_info.locked_until);
            self.log_failed_attempt(&request.email, Some(user.id), ip_address, "Account locked", true).await?;
            return Err(AppError::Unauthorized(format!(
                "Account is locked until {}",
                lockout_info.locked_until
                    .map(|dt| dt.format("%Y-%m-%d %H:%M:%S UTC").to_string())
                    .unwrap_or_else(|| "unknown".to_string())
            )));
        }

        // Verify password
        let password_valid = self.password_service.verify_password(&request.password, &user.password_hash)
            .map_err(|e| {
                error!("Failed to verify password: {}", e);
                AppError::InternalServerError("Authentication service unavailable".to_string())
            })?;

        if !password_valid {
            warn!("Authentication failed: invalid password for user {}", user.id);

            // Record failed attempt and check if account should be locked
            let lockout_result = self.lockout_service.record_failed_attempt(
                user.id,
                ip_address,
                "Invalid password"
            ).await.map_err(|e| {
                error!("Failed to record failed attempt: {}", e);
                AppError::InternalServerError("Authentication service unavailable".to_string())
            })?;

            self.log_failed_attempt(&request.email, Some(user.id), ip_address, "Invalid password", lockout_result.is_locked).await?;

            if lockout_result.is_locked {
                return Err(AppError::Unauthorized(format!(
                    "Account locked due to too many failed attempts. Locked until {}",
                    lockout_result.locked_until
                        .map(|dt| dt.format("%Y-%m-%d %H:%M:%S UTC").to_string())
                        .unwrap_or_else(|| "unknown".to_string())
                )));
            }

            return Err(AppError::Unauthorized("Invalid credentials".to_string()));
        }

        // Authentication successful - clear any failed attempts
        if let Err(e) = self.lockout_service.record_successful_attempt(user.id).await {
            error!("Failed to record successful attempt: {}", e);
            // Don't fail authentication for this, just log the error
        }

        // Check if MFA is enabled for this user
        let mfa_enabled = self.mfa_service.is_mfa_enabled(user.id).await
            .map_err(|e| {
                error!("Failed to check MFA status: {}", e);
                AppError::InternalServerError("Authentication service unavailable".to_string())
            })?;

        if mfa_enabled {
            // MFA is required - generate temporary MFA token
            let mfa_token = self.generate_mfa_token(&user);

            info!("Authentication successful for user {}, MFA required", user.id);
            return Ok(AuthLoginResponse {
                user,
                tokens: None,
                mfa_required: true,
                mfa_token: Some(mfa_token),
            });
        }

        // No MFA required - update last login and generate tokens
        let updated_user = self.update_last_login(user.id, ip_address).await?;
        let tokens = self.generate_tokens(&updated_user, request.remember_me.unwrap_or(false)).await
            .map_err(|e| {
                error!("Failed to generate tokens: {}", e);
                AppError::InternalServerError("Failed to generate authentication tokens".to_string())
            })?;

        info!("Authentication successful for user {}", user.id);
        Ok(AuthLoginResponse {
            user: updated_user,
            tokens: Some(tokens),
            mfa_required: false,
            mfa_token: None,
        })
    }

    /// Get user by ID
    pub async fn get_user_by_id(&self, user_id: Uuid) -> Result<Option<User>, AppError> {
        let query = "SELECT * FROM users WHERE id = $1 AND is_active = true";

        let user = sqlx::query_as::<_, User>(query)
            .bind(user_id)
            .fetch_optional(&self.pool)
            .await
            .map_err(|e| {
                error!("Failed to get user by ID: {}", e);
                AppError::InternalServerError("Failed to retrieve user".to_string())
            })?;

        Ok(user)
    }

    /// Get user by email
    pub async fn get_user_by_email(&self, email: &str) -> Result<Option<User>, AppError> {
        let query = "SELECT * FROM users WHERE email = $1";

        let user = sqlx::query_as::<_, User>(query)
            .bind(email)
            .fetch_optional(&self.pool)
            .await
            .map_err(|e| {
                error!("Failed to get user by email: {}", e);
                AppError::InternalServerError("Failed to retrieve user".to_string())
            })?;

        Ok(user)
    }

    /// Check if user exists by email
    async fn user_exists_by_email(&self, email: &str) -> Result<bool, AppError> {
        let query = "SELECT EXISTS(SELECT 1 FROM users WHERE email = $1)";

        let exists: (bool,) = sqlx::query_as(query)
            .bind(email)
            .fetch_one(&self.pool)
            .await
            .map_err(|e| {
                error!("Failed to check if user exists by email: {}", e);
                AppError::InternalServerError("Database error".to_string())
            })?;

        Ok(exists.0)
    }

    /// Check if user exists by username
    async fn user_exists_by_username(&self, username: &str) -> Result<bool, AppError> {
        let query = "SELECT EXISTS(SELECT 1 FROM users WHERE username = $1)";

        let exists: (bool,) = sqlx::query_as(query)
            .bind(username)
            .fetch_one(&self.pool)
            .await
            .map_err(|e| {
                error!("Failed to check if user exists by username: {}", e);
                AppError::InternalServerError("Database error".to_string())
            })?;

        Ok(exists.0)
    }

    /// Update last login information and reset failed login attempts
    pub async fn update_last_login(&self, user_id: Uuid, ip_address: &str) -> Result<User, AppError> {
        let now = Utc::now();
        let query = r"
            UPDATE users
            SET last_login_at = $1, 
                last_login_ip = $2, 
                updated_at = $1,
                failed_login_attempts = 0,  // Reset failed attempts on successful login
                locked_until = NULL          // Clear any lock
            WHERE id = $3
            RETURNING *
        ";

        let user = sqlx::query_as::<_, User>(query)
            .bind(now)
            .bind(ip_address)
            .bind(user_id)
            .fetch_one(&self.pool)
            .await
            .map_err(|e| {
                error!("Failed to update last login for user {}: {}", user_id, e);
                AppError::InternalServerError("Failed to update last login".to_string())
            })?;

        info!("Updated last login for user {} from IP {}", user_id, ip_address);
        Ok(user)
    }

    /// Log a failed authentication attempt
    async fn log_failed_attempt(
        &self,
        email: &str,
        user_id: Option<Uuid>,
        ip_address: &str,
        failure_reason: &str,
        blocked_by_rate_limit: bool,
    ) -> Result<(), AppError> {
        let attempt_request = LoginAttemptRequest {
            email: Some(email.to_string()),
            user_id,
            ip_address: ip_address.to_string(),
            user_agent: None, // This would come from the HTTP request
            success: false,
            failure_reason: Some(failure_reason.to_string()),
            mfa_required: false,
            mfa_success: None,
            suspicious_activity: false,
            blocked_by_rate_limit,
            country_code: None, // Could be added with IP geolocation
            duration_ms: None,
        };

        let query = r"
            INSERT INTO login_attempts (
                id, email, user_id, ip_address, user_agent, success, failure_reason,
                mfa_required, mfa_success, suspicious_activity, blocked_by_rate_limit,
                country_code, attempted_at, duration_ms
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
        ";

        sqlx::query(query)
            .bind(Uuid::new_v4())
            .bind(&attempt_request.email)
            .bind(&attempt_request.user_id)
            .bind(&attempt_request.ip_address)
            .bind(&attempt_request.user_agent)
            .bind(attempt_request.success)
            .bind(&attempt_request.failure_reason)
            .bind(attempt_request.mfa_required)
            .bind(&attempt_request.mfa_success)
            .bind(attempt_request.suspicious_activity)
            .bind(attempt_request.blocked_by_rate_limit)
            .bind(&attempt_request.country_code)
            .bind(Utc::now())
            .bind(&attempt_request.duration_ms)
            .execute(&self.pool)
            .await
            .map_err(|e| {
                error!("Failed to log failed attempt: {}", e);
                // Don't fail the authentication flow for logging errors
                AppError::InternalServerError("Logging error".to_string())
            })?;

        Ok(())
    }

    /// Change user password
    pub async fn change_password(
        &self,
        user_id: Uuid,
        current_password: &str,
        new_password: &str,
    ) -> Result<(), AppError> {
        // Get current user
        let user = self.get_user_by_id(user_id).await?
            .ok_or_else(|| AppError::NotFound("User not found".to_string()))?;

        // Verify current password
        let password_valid = self.password_service.verify_password(current_password, &user.password_hash)
            .map_err(|e| {
                error!("Failed to verify current password: {}", e);
                AppError::InternalServerError("Password verification failed".to_string())
            })?;

        if !password_valid {
            return Err(AppError::Unauthorized("Current password is incorrect".to_string()));
        }

        // Validate new password strength
        let password_analysis = self.password_service.analyze_password_strength(new_password);
        if password_analysis.score < 60 {
            let mut issues = Vec::new();
            if !password_analysis.has_lowercase { issues.push("missing lowercase letters"); }
            if !password_analysis.has_uppercase { issues.push("missing uppercase letters"); }
            if !password_analysis.has_numbers { issues.push("missing numbers"); }
            if !password_analysis.has_symbols { issues.push("missing symbols"); }
            if password_analysis.is_common { issues.push("password is too common"); }
            if password_analysis.length < 8 { issues.push("password is too short"); }

            return Err(AppError::BadRequest(format!(
                "New password is too weak. Score: {}/100. Issues: {}",
                password_analysis.score,
                if issues.is_empty() { "low entropy".to_string() } else { issues.join(", ") }
            )));
        }

        // Hash new password
        let new_password_hash = self.password_service.hash_password(new_password)
            .map_err(|e| {
                error!("Failed to hash new password: {}", e);
                AppError::InternalServerError("Failed to process new password".to_string())
            })?;

        // Update password in database
        let now = Utc::now();
        let query = r"
            UPDATE users
            SET password_hash = $1, password_changed_at = $2, updated_at = $2, must_change_password = false
            WHERE id = $3
        ";

        sqlx::query(query)
            .bind(&new_password_hash)
            .bind(now)
            .bind(user_id)
            .execute(&self.pool)
            .await
            .map_err(|e| {
                error!("Failed to update password: {}", e);
                AppError::InternalServerError("Failed to update password".to_string())
            })?;

        info!("Password changed successfully for user {}", user_id);
        Ok(())
    }

    /// Deactivate user account
    pub async fn deactivate_user(&self, user_id: Uuid, admin_user_id: Uuid) -> Result<(), AppError> {
        let query = r"
            UPDATE users
            SET is_active = false, updated_at = $1, updated_by = $2
            WHERE id = $3
        ";

        sqlx::query(query)
            .bind(Utc::now())
            .bind(admin_user_id)
            .bind(user_id)
            .execute(&self.pool)
            .await
            .map_err(|e| {
                error!("Failed to deactivate user: {}", e);
                AppError::InternalServerError("Failed to deactivate user".to_string())
            })?;

        info!("User {} deactivated by admin {}", user_id, admin_user_id);
        Ok(())
    }

    /// Activate user account
    pub async fn activate_user(&self, user_id: Uuid, admin_user_id: Uuid) -> Result<(), AppError> {
        let now = Utc::now();
        sqlx::query!(
            "UPDATE users SET is_active = true, updated_at = $1, updated_by = $2 WHERE id = $3",
            now,
            admin_user_id,
            user_id
        )
        .execute(&self.pool)
        .await
        .map_err(|e| {
            error!("Failed to activate user: {}", e);
            AppError::InternalServerError("Failed to activate user".to_string())
        })?;

        info!("User {} activated by admin {}", user_id, admin_user_id);
        Ok(())
    }

    /// Complete MFA verification and generate tokens
    pub async fn complete_mfa_verification(
        &self,
        user_id: Uuid,
        mfa_request: MfaVerifyRequest,
        ip_address: Option<IpAddr>,
        user_agent: Option<String>,
        device_fingerprint: Option<String>,
        remember_me: bool,
    ) -> Result<AuthTokens, AppError> {
        // Verify MFA using the MFA service
        let mfa_response = self.mfa_service.verify_mfa(
            user_id,
            mfa_request,
            ip_address,
            user_agent,
            device_fingerprint,
        ).await.map_err(|e| {
            error!("MFA verification failed: {}", e);
            AppError::Unauthorized("Invalid MFA code".to_string())
        })?;

        if !mfa_response.success {
            return Err(AppError::Unauthorized("Invalid MFA code".to_string()));
        }

        // Update last login information and get the updated user
        let ip_str = ip_address.map(|ip| ip.to_string()).unwrap_or_else(|| "unknown".to_string());
        let updated_user = self.update_last_login(user_id, &ip_str).await?;

        // Generate tokens
        let tokens = self.generate_tokens(&updated_user, remember_me).await
            .map_err(|e| {
                error!("Failed to generate tokens after MFA: {}", e);
                AppError::InternalServerError("Failed to generate authentication tokens".to_string())
            })?;

        info!("MFA verification successful for user {}", user_id);
        Ok(tokens)
    }

    /// Generate JWT access and refresh tokens for a user (delegates to JWT service)
    pub async fn generate_tokens(&self, user: &User, remember_me: bool) -> Result<AuthTokens, AppError> {
        // Create a session ID for the tokens
        let session_id = Uuid::new_v4();

        // Determine token duration based on remember_me
        let access_duration = if remember_me {
            chrono::Duration::days(30)
        } else {
            chrono::Duration::hours(24)
        };

        // Create token pair using JWT service
        let token_pair = self.jwt_service.create_token_pair(
            user.id,
            user.email.clone(),
            vec![], // roles - would be populated from RBAC service
            vec![], // permissions - would be populated from RBAC service
            session_id,
            false, // mfa_verified - set to false initially, will be true after MFA
            None, // device_id
            None, // ip_address
            None, // user_agent
            false, // trusted_device
        ).map_err(|e| {
            error!("Failed to create token pair: {}", e);
            AppError::InternalServerError("Failed to generate authentication tokens".to_string())
        })?;

        Ok(AuthTokens {
            access_token: token_pair.access_token,
            refresh_token: token_pair.refresh_token,
            expires_in: access_duration.num_seconds(),
        })
    }

    /// Generate temporary MFA token for a user (valid for a short time)
    pub fn generate_mfa_token(&self, user: &User) -> String {
        use rand::Rng;

        // Generate a secure random token
        let mut rng = rand::rng();
        let token_bytes: Vec<u8> = (0..32).map(|_| rng.random()).collect();

        // Create token data with user ID and timestamp
        let token_data = format!(
            "{}:{}:{}",
            user.id,
            Utc::now().timestamp(),
            general_purpose::URL_SAFE.encode(&token_bytes)
        );

        // Hash the token data for security
        let hash = self.password_service.hash_password(&token_data)
            .unwrap_or_else(|_| {
                error!("Failed to hash MFA token, using fallback");
                format!("mfa-fallback-{}-{}", user.id, Utc::now().timestamp())
            });

        // Return a URL-safe base64 encoded version of the hash
        general_purpose::URL_SAFE.encode(hash.as_bytes())
    }

    /// Check if MFA is enabled for a user
    pub async fn is_mfa_enabled(&self, user_id: Uuid) -> Result<bool, AppError> {
        self.mfa_service.is_mfa_enabled(user_id).await
            .map_err(|e| {
                error!("Failed to check MFA status: {}", e);
                AppError::InternalServerError("Failed to check MFA status".to_string())
            })
    }

    /// Get MFA status for a user
    pub async fn get_mfa_status(&self, user_id: Uuid) -> Result<crate::models::MfaStatusResponse, AppError> {
        self.mfa_service.get_mfa_status(user_id).await
            .map_err(|e| {
                error!("Failed to get MFA status: {}", e);
                AppError::InternalServerError("Failed to get MFA status".to_string())
            })
    }

    /// Invalidate a JWT token by adding it to the blacklist
    ///
    /// This method adds the provided JWT token to a Redis-based blacklist
    /// to prevent its further use. The token will be blacklisted until its
    /// natural expiration time.
    ///
    /// # Arguments
    /// * `token` - The JWT token to invalidate
    ///
    /// # Returns
    /// * `Result<(), AppError>` - Success or error details
    ///
    /// # Security Notes
    /// - Tokens are stored in Redis with TTL matching their expiration
    /// - This prevents replay attacks with stolen tokens
    /// - Blacklisted tokens are checked during token validation
    pub async fn invalidate_token(&self, token: &str) -> Result<(), AppError> {
        // Decode the token to get its expiration time (without validating signature)
        let token_data = jsonwebtoken::decode::<crate::services::jwt_service::Claims>(
            token,
            &jsonwebtoken::DecodingKey::from_secret(&[]), // Dummy key for header/claims extraction
            &jsonwebtoken::Validation::new(jsonwebtoken::Algorithm::HS256),
        );

        // Calculate TTL based on token expiration, or use default if decode fails
        let ttl_seconds = match token_data {
            Ok(data) => {
                let exp = data.claims.exp as i64;
                let now = Utc::now().timestamp();
                if exp > now {
                    exp - now
                } else {
                    // Token already expired, but blacklist briefly to prevent immediate reuse
                    300 // 5 minutes
                }
            }
            Err(_) => {
                // If we can't decode the token, blacklist it for a reasonable time
                warn!("Failed to decode token for blacklisting, using default TTL");
                3600 // 1 hour
            }
        };

        // Add token to Redis blacklist
        let mut conn = self.get_redis_connection()?;
        let blacklist_key = format!("blacklisted_token:{}", token);

        let _: () = conn.set_ex(&blacklist_key, "1", ttl_seconds as u64)
            .map_err(|e| {
                error!("Failed to blacklist token: {}", e);
                AppError::InternalServerError("Failed to invalidate token".to_string())
            })?;

        info!("Token blacklisted successfully with TTL: {} seconds", ttl_seconds);
        Ok(())
    }

    /// Check if a token is blacklisted
    ///
    /// This method checks if the provided JWT token exists in the Redis blacklist.
    /// This should be called during token validation to ensure revoked tokens
    /// are not accepted.
    ///
    /// # Arguments
    /// * `token` - The JWT token to check
    ///
    /// # Returns
    /// * `Result<bool, AppError>` - True if token is blacklisted, false otherwise
    pub async fn is_token_blacklisted(&self, token: &str) -> Result<bool, AppError> {
        let mut conn = self.get_redis_connection()?;
        let blacklist_key = format!("blacklisted_token:{}", token);

        let exists: bool = conn.exists(&blacklist_key)
            .map_err(|e| {
                error!("Failed to check token blacklist: {}", e);
                AppError::InternalServerError("Failed to check token status".to_string())
            })?;

        Ok(exists)
    }

    /// Get Redis connection
    ///
    /// Helper method to get a Redis connection for token blacklisting operations.
    ///
    /// # Returns
    /// * `Result<Connection, AppError>` - Redis connection or error
    fn get_redis_connection(&self) -> Result<Connection, AppError> {
        self.redis_client.get_connection()
            .map_err(|e| {
                error!("Failed to get Redis connection: {}", e);
                AppError::InternalServerError("Cache service unavailable".to_string())
            })
    }
}
