use argon2::{Argon2, Pass<PERSON><PERSON><PERSON>, Pass<PERSON><PERSON>ash<PERSON>, PasswordVerifier};
use argon2::password_hash::{rand_core::OsRng, SaltString};
use anyhow::Result;
use thiserror::Error;
use std::collections::HashSet;
use sqlx::{PgPool, Row};
use uuid::Uuid;
use chrono::Utc;
use tracing::{info, warn, error};
// use regex::Regex; // For future password pattern validation

#[derive(Error, Debug)]
pub enum PasswordError {
    #[error("Password hashing failed: {0}")]
    HashingFailed(String),
    #[error("Password verification failed: {0}")]
    VerificationFailed(String),
    #[error("Password validation failed: {0}")]
    ValidationFailed(String),
    #[error("Password is too weak: {0}")]
    WeakPassword(String),
    #[error("Password has been used recently")]
    PasswordReused,
}

#[derive(Clone)]
pub struct PasswordService {
    argon2: Argon2<'static>,
    common_passwords: HashSet<String>,
    pool: Option<PgPool>,
}

#[derive(Debug, Clone)]
pub struct PasswordStrength {
    pub score: u8, // 0-100
    pub has_lowercase: bool,
    pub has_uppercase: bool,
    pub has_numbers: bool,
    pub has_symbols: bool,
    pub length: usize,
    pub entropy: f64,
    pub is_common: bool,
}

#[derive(Debug, Clone)]
pub struct PasswordPolicy {
    pub min_length: usize,
    pub max_length: usize,
    pub require_lowercase: bool,
    pub require_uppercase: bool,
    pub require_numbers: bool,
    pub require_symbols: bool,
    pub min_entropy: f64,
    pub prevent_common: bool,
    pub prevent_reuse_count: usize,
}

impl Default for PasswordPolicy {
    fn default() -> Self {
        Self {
            min_length: 8,
            max_length: 128,
            require_lowercase: true,
            require_uppercase: true,
            require_numbers: true,
            require_symbols: true,
            min_entropy: 3.0,
            prevent_common: true,
            prevent_reuse_count: 5,
        }
    }
}

impl PasswordService {
    pub fn new() -> Self {
        let common_passwords = Self::load_common_passwords();

        Self {
            argon2: Argon2::default(),
            common_passwords,
            pool: None,
        }
    }

    pub fn new_with_db(pool: PgPool) -> Self {
        let common_passwords = Self::load_common_passwords();

        Self {
            argon2: Argon2::default(),
            common_passwords,
            pool: Some(pool),
        }
    }

    /// Hash a password using Argon2
    pub fn hash_password(&self, password: &str) -> Result<String, PasswordError> {
        let salt = SaltString::generate(&mut OsRng);
        
        self.argon2
            .hash_password(password.as_bytes(), &salt)
            .map(|hash| hash.to_string())
            .map_err(|e| PasswordError::HashingFailed(e.to_string()))
    }

    /// Verify a password against its hash
    pub fn verify_password(&self, password: &str, hash: &str) -> Result<bool, PasswordError> {
        let parsed_hash = PasswordHash::new(hash)
            .map_err(|e| PasswordError::VerificationFailed(e.to_string()))?;
        
        Ok(self.argon2.verify_password(password.as_bytes(), &parsed_hash).is_ok())
    }

    /// Validate password against policy
    pub fn validate_password(&self, password: &str, policy: &PasswordPolicy) -> Result<(), PasswordError> {
        let strength = self.analyze_password_strength(password);
        
        // Check length
        if password.len() < policy.min_length {
            return Err(PasswordError::ValidationFailed(
                format!("Password must be at least {} characters long", policy.min_length)
            ));
        }
        
        if password.len() > policy.max_length {
            return Err(PasswordError::ValidationFailed(
                format!("Password must be no more than {} characters long", policy.max_length)
            ));
        }

        // Check character requirements
        if policy.require_lowercase && !strength.has_lowercase {
            return Err(PasswordError::ValidationFailed(
                "Password must contain at least one lowercase letter".to_string()
            ));
        }

        if policy.require_uppercase && !strength.has_uppercase {
            return Err(PasswordError::ValidationFailed(
                "Password must contain at least one uppercase letter".to_string()
            ));
        }

        if policy.require_numbers && !strength.has_numbers {
            return Err(PasswordError::ValidationFailed(
                "Password must contain at least one number".to_string()
            ));
        }

        if policy.require_symbols && !strength.has_symbols {
            return Err(PasswordError::ValidationFailed(
                "Password must contain at least one symbol".to_string()
            ));
        }

        // Check entropy
        if strength.entropy < policy.min_entropy {
            return Err(PasswordError::WeakPassword(
                format!("Password entropy too low: {:.2} (minimum: {:.2})", 
                       strength.entropy, policy.min_entropy)
            ));
        }

        // Check against common passwords
        if policy.prevent_common && strength.is_common {
            return Err(PasswordError::WeakPassword(
                "Password is too common and easily guessable".to_string()
            ));
        }

        Ok(())
    }

    /// Validate password against history to prevent reuse
    ///
    /// This method checks if the provided password has been used recently
    /// by the user. It compares against the configured number of previous
    /// passwords stored in the password_history table.
    ///
    /// # Arguments
    /// * `user_id` - The ID of the user
    /// * `new_password` - The new password to validate
    /// * `policy` - The password policy containing reuse prevention settings
    ///
    /// # Returns
    /// * `Result<(), PasswordError>` - Success or password reuse error
    pub async fn validate_password_history(
        &self,
        user_id: Uuid,
        new_password: &str,
        policy: &PasswordPolicy,
    ) -> Result<(), PasswordError> {
        // If no database pool is available or reuse count is 0, skip validation
        let pool = match &self.pool {
            Some(pool) => pool,
            None => {
                warn!("Password history validation skipped: no database connection");
                return Ok(());
            }
        };

        if policy.prevent_reuse_count == 0 {
            return Ok(());
        }

        // Get the most recent password hashes for this user
        let query = r#"
            SELECT password_hash
            FROM password_history
            WHERE user_id = $1
            ORDER BY created_at DESC
            LIMIT $2
        "#;

        let rows = sqlx::query(query)
            .bind(user_id)
            .bind(policy.prevent_reuse_count as i64)
            .fetch_all(pool)
            .await
            .map_err(|e| {
                error!("Failed to fetch password history: {}", e);
                PasswordError::ValidationFailed("Unable to validate password history".to_string())
            })?;

        // Check if the new password matches any of the previous passwords
        for row in rows {
            let stored_hash: String = row.get("password_hash");

            if self.verify_password(new_password, &stored_hash)? {
                return Err(PasswordError::PasswordReused);
            }
        }

        Ok(())
    }

    /// Store password in history
    ///
    /// This method stores a password hash in the password history table
    /// for future reuse prevention. It also cleans up old entries beyond
    /// the configured retention limit.
    ///
    /// # Arguments
    /// * `user_id` - The ID of the user
    /// * `password_hash` - The hashed password to store
    /// * `policy` - The password policy containing retention settings
    ///
    /// # Returns
    /// * `Result<(), PasswordError>` - Success or database error
    pub async fn store_password_in_history(
        &self,
        user_id: Uuid,
        password_hash: &str,
        policy: &PasswordPolicy,
    ) -> Result<(), PasswordError> {
        let pool = match &self.pool {
            Some(pool) => pool,
            None => {
                warn!("Password history storage skipped: no database connection");
                return Ok(());
            }
        };

        // Insert the new password hash
        let insert_query = r#"
            INSERT INTO password_history (user_id, password_hash, created_at)
            VALUES ($1, $2, $3)
        "#;

        sqlx::query(insert_query)
            .bind(user_id)
            .bind(password_hash)
            .bind(Utc::now())
            .execute(pool)
            .await
            .map_err(|e| {
                error!("Failed to store password in history: {}", e);
                PasswordError::ValidationFailed("Unable to store password history".to_string())
            })?;

        // Clean up old entries beyond the retention limit
        if policy.prevent_reuse_count > 0 {
            let cleanup_query = r#"
                DELETE FROM password_history
                WHERE user_id = $1
                AND id NOT IN (
                    SELECT id
                    FROM password_history
                    WHERE user_id = $1
                    ORDER BY created_at DESC
                    LIMIT $2
                )
            "#;

            sqlx::query(cleanup_query)
                .bind(user_id)
                .bind(policy.prevent_reuse_count as i64)
                .execute(pool)
                .await
                .map_err(|e| {
                    error!("Failed to cleanup old password history: {}", e);
                    // Don't fail the operation for cleanup errors
                    PasswordError::ValidationFailed("Failed to cleanup password history".to_string())
                })?;
        }

        info!("Password stored in history for user: {}", user_id);
        Ok(())
    }

    /// Analyze password strength
    pub fn analyze_password_strength(&self, password: &str) -> PasswordStrength {
        let has_lowercase = password.chars().any(|c| c.is_lowercase());
        let has_uppercase = password.chars().any(|c| c.is_uppercase());
        let has_numbers = password.chars().any(|c| c.is_numeric());
        let has_symbols = password.chars().any(|c| !c.is_alphanumeric());
        let length = password.len();
        let is_common = self.is_common_password(password);
        
        let entropy = self.calculate_entropy(password);
        
        // Calculate score (0-100)
        let mut score = 0u8;
        
        // Length scoring (0-30 points)
        score += match length {
            0..=7 => 0,
            8..=11 => 10,
            12..=15 => 20,
            _ => 30,
        };
        
        // Character variety (0-40 points)
        if has_lowercase { score += 10; }
        if has_uppercase { score += 10; }
        if has_numbers { score += 10; }
        if has_symbols { score += 10; }
        
        // Entropy bonus (0-20 points)
        score += match entropy {
            e if e < 2.0 => 0,
            e if e < 3.0 => 5,
            e if e < 4.0 => 10,
            e if e < 5.0 => 15,
            _ => 20,
        };
        
        // Common password penalty (0-10 points)
        if !is_common { score += 10; }
        
        PasswordStrength {
            score,
            has_lowercase,
            has_uppercase,
            has_numbers,
            has_symbols,
            length,
            entropy,
            is_common,
        }
    }

    /// Calculate password entropy
    fn calculate_entropy(&self, password: &str) -> f64 {
        if password.is_empty() {
            return 0.0;
        }

        let mut charset_size = 0;
        
        if password.chars().any(|c| c.is_lowercase()) {
            charset_size += 26;
        }
        if password.chars().any(|c| c.is_uppercase()) {
            charset_size += 26;
        }
        if password.chars().any(|c| c.is_numeric()) {
            charset_size += 10;
        }
        if password.chars().any(|c| !c.is_alphanumeric()) {
            charset_size += 32; // Common symbols
        }

        if charset_size == 0 {
            return 0.0;
        }

        (password.len() as f64) * (charset_size as f64).log2()
    }

    /// Check if password is in common passwords list
    fn is_common_password(&self, password: &str) -> bool {
        self.common_passwords.contains(&password.to_lowercase())
    }

    /// Load common passwords (in production, load from file)
    fn load_common_passwords() -> HashSet<String> {
        let mut passwords = HashSet::new();
        
        // Add some common passwords for demonstration
        let common = vec![
            "password", "123456", "password123", "admin", "qwerty",
            "letmein", "welcome", "monkey", "1234567890", "abc123",
            "password1", "123456789", "welcome123", "admin123",
            "root", "toor", "pass", "test", "guest", "user",
        ];
        
        for pwd in common {
            passwords.insert(pwd.to_string());
        }
        
        passwords
    }

    /// Generate a secure random password
    pub fn generate_secure_password(&self, length: usize) -> String {
        use rand::Rng;
        
        let charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*";
        let mut rng = rand::thread_rng();
        
        (0..length)
            .map(|_| {
                let idx = rng.random_range(0..charset.len());
                charset.chars().nth(idx).unwrap()
            })
            .collect()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_password_hashing_and_verification() {
        let service = PasswordService::new();
        let password = "test_password_123!";
        
        let hash = service.hash_password(password).unwrap();
        assert!(service.verify_password(password, &hash).unwrap());
        assert!(!service.verify_password("wrong_password", &hash).unwrap());
    }

    #[test]
    fn test_password_strength_analysis() {
        let service = PasswordService::new();
        
        let weak = service.analyze_password_strength("123");
        assert!(weak.score < 50);
        
        let strong = service.analyze_password_strength("MyStr0ng!P@ssw0rd");
        assert!(strong.score > 80);
        assert!(strong.has_lowercase);
        assert!(strong.has_uppercase);
        assert!(strong.has_numbers);
        assert!(strong.has_symbols);
    }

    #[test]
    fn test_password_validation() {
        let service = PasswordService::new();
        let policy = PasswordPolicy::default();
        
        // Should fail - too short
        assert!(service.validate_password("123", &policy).is_err());
        
        // Should fail - no uppercase
        assert!(service.validate_password("password123!", &policy).is_err());
        
        // Should pass
        assert!(service.validate_password("MyStr0ng!P@ssw0rd", &policy).is_ok());
    }
}
