use std::collections::HashMap;
use std::sync::Arc;
use sqlx::PgPool;
use chrono::{Utc, Duration};
use tracing::{info, warn, error, debug};
use uuid::Uuid;
use async_trait::async_trait;

use crate::models::oauth::*;
use crate::config::{OAuthConfig};
use crate::services::AuthService;

// OAuth Provider Implementations
use oauth2::{
    AuthUrl, ClientId, ClientSecret, CsrfToken, PkceCodeChallenge, PkceCodeVerifier,
     Scope, TokenUrl, AuthorizationCode, TokenResponse,
};
use oauth2::basic::BasicClient;
use oauth2::reqwest;

/// Configuration for an OAuth provider
#[derive(Debug, Clone)]
pub struct ProviderConfig {
    pub client_id: String,
    pub client_secret: String,
    pub auth_url: String,
    pub token_url: String,
    pub user_info_url: String,
    pub scopes: Vec<String>,
    pub supports_pkce: bool,
    pub requires_user_agent: bool,
    pub redirect_uris: Vec<String>,
    pub additional_fields: std::collections::HashMap<String, String>,
}

/// Trait for OAuth provider implementations
#[async_trait]
pub trait OAuthProvider: Send + Sync {
    /// Get the provider name
    fn name(&self) -> &str;

    /// Get the provider configuration
    fn config(&self) -> &ProviderConfig;

    /// Parse user info from the provider's response
    async fn parse_user_info(&self, user_data: serde_json::Value, access_token: &str) -> Result<OAuthUserInfo, OAuthError>;

    /// Get additional headers required for API calls (e.g., User-Agent for GitHub)
    fn get_api_headers(&self) -> Vec<(String, String)> {
        Vec::new()
    }

    /// Get authorization URL with provider-specific logic
    fn get_auth_url(&self, state: &str, code_verifier: Option<&str>) -> Result<String, OAuthError> {
        let config = self.config();
        let client = BasicClient::new(ClientId::new(config.client_id.clone()))
            .set_client_secret(ClientSecret::new(config.client_secret.clone()))
            .set_auth_uri(AuthUrl::new(config.auth_url.clone())?)
            .set_token_uri(TokenUrl::new(config.token_url.clone())?);

        let mut auth_request = client.authorize_url(|| CsrfToken::new(state.to_string()));

        // Add scopes
        for scope_str in &config.scopes {
            auth_request = auth_request.add_scope(Scope::new(scope_str.clone()));
        }

        // Add PKCE if supported and verifier is provided
        if config.supports_pkce {
            if let Some(verifier) = code_verifier {
                let pkce_verifier = PkceCodeVerifier::new(verifier.to_string());
                let pkce_challenge = PkceCodeChallenge::from_code_verifier_sha256(&pkce_verifier);
                auth_request = auth_request.set_pkce_challenge(pkce_challenge);
            }
        }

        let (auth_url, _) = auth_request.url();
        Ok(auth_url.to_string())
    }

    /// Exchange authorization code for tokens
    async fn exchange_code(&self, code: &str, code_verifier: Option<&str>) -> Result<OAuthTokenResponse, OAuthError> {
        let config = self.config();
        let client = BasicClient::new(ClientId::new(config.client_id.clone()))
            .set_client_secret(ClientSecret::new(config.client_secret.clone()))
            .set_auth_uri(AuthUrl::new(config.auth_url.clone())?)
            .set_token_uri(TokenUrl::new(config.token_url.clone())?);

        let mut token_request = client.exchange_code(AuthorizationCode::new(code.to_string()));

        // Add PKCE verifier if supported and provided
        if config.supports_pkce {
            if let Some(verifier) = code_verifier {
                token_request = token_request.set_pkce_verifier(PkceCodeVerifier::new(verifier.to_string()));
            }
        }

        let http_client = reqwest::Client::new();
        let token_response = token_request
            .request_async(&http_client)
            .await
            .map_err(|e| OAuthError::TokenExchangeFailed(e.to_string()))?;

        Ok(OAuthTokenResponse {
            access_token: token_response.access_token().secret().clone(),
            refresh_token: token_response.refresh_token().map(|t| t.secret().clone()),
            expires_in: token_response.expires_in().map(|d| d.as_secs() as i64),
            token_type: "Bearer".to_string(),
            scope: token_response.scopes().map(|scopes| {
                scopes.iter().map(|s| s.as_str()).collect::<Vec<_>>().join(" ")
            }),
        })
    }

    /// Get user info from the provider
    async fn get_user_info(&self, access_token: &str) -> Result<OAuthUserInfo, OAuthError> {
        let client = reqwest::Client::new();
        let mut request = client
            .get(&self.config().user_info_url)
            .bearer_auth(access_token);

        // Add provider-specific headers
        for (key, value) in self.get_api_headers() {
            request = request.header(&key, &value);
        }

        let response = request
            .send()
            .await
            .map_err(|e| {
                error!("Failed to fetch {} user info: {}", self.name(), e);
                OAuthError::HttpError(e)
            })?;

        if !response.status().is_success() {
            error!("{} user info request failed with status: {}", self.name(), response.status());
            return Err(OAuthError::UserInfoFailed(format!("HTTP {}", response.status())));
        }

        let user_data: serde_json::Value = response.json().await
            .map_err(|e| {
                error!("Failed to parse {} user info JSON: {}", self.name(), e);
                OAuthError::HttpError(e)
            })?;

        self.parse_user_info(user_data, access_token).await
    }


}

// Concrete provider implementations

/// Google OAuth provider
pub struct GoogleProvider {
    config: ProviderConfig,
}

impl GoogleProvider {
    pub fn new(client_id: String, client_secret: String) -> Self {
        Self::new_with_config(client_id, client_secret, None, None)
    }

    pub fn new_with_config(
        client_id: String,
        client_secret: String,
        custom_scopes: Option<Vec<String>>,
        redirect_uris: Option<Vec<String>>
    ) -> Self {
        let scopes = custom_scopes.unwrap_or_else(|| {
            vec!["openid".to_string(), "email".to_string(), "profile".to_string()]
        });

        let redirect_uris = redirect_uris.unwrap_or_else(|| {
            vec!["/auth/oauth/google/callback".to_string()]
        });

        Self {
            config: ProviderConfig {
                client_id,
                client_secret,
                auth_url: "https://accounts.google.com/o/oauth2/v2/auth".to_string(),
                token_url: "https://www.googleapis.com/oauth2/v4/token".to_string(),
                user_info_url: "https://www.googleapis.com/oauth2/v2/userinfo".to_string(),
                scopes,
                supports_pkce: true,
                requires_user_agent: false,
                redirect_uris,
                additional_fields: std::collections::HashMap::new(),
            },
        }
    }
}

#[async_trait]
impl OAuthProvider for GoogleProvider {
    fn name(&self) -> &str { "google" }
    fn config(&self) -> &ProviderConfig { &self.config }

    async fn parse_user_info(&self, user_data: serde_json::Value, _access_token: &str) -> Result<OAuthUserInfo, OAuthError> {
        Ok(OAuthUserInfo {
            id: user_data["id"].as_str().unwrap_or_default().to_string(),
            email: user_data["email"].as_str().map(|s| s.to_string()),
            name: user_data["name"].as_str().map(|s| s.to_string()),
            picture: user_data["picture"].as_str().map(|s| s.to_string()),
            verified_email: user_data["verified_email"].as_bool(),
        })
    }
}

/// GitHub OAuth provider
pub struct GitHubProvider {
    config: ProviderConfig,
}

impl GitHubProvider {
    pub fn new(client_id: String, client_secret: String) -> Self {
        Self::new_with_config(client_id, client_secret, None, None)
    }

    pub fn new_with_config(
        client_id: String,
        client_secret: String,
        custom_scopes: Option<Vec<String>>,
        redirect_uris: Option<Vec<String>>
    ) -> Self {
        let scopes = custom_scopes.unwrap_or_else(|| {
            vec!["user:email".to_string()]
        });

        let redirect_uris = redirect_uris.unwrap_or_else(|| {
            vec!["/auth/oauth/github/callback".to_string()]
        });

        // GitHub-specific additional fields that may be needed
        let mut additional_fields = std::collections::HashMap::new();
        additional_fields.insert("allow_signup".to_string(), "true".to_string());

        Self {
            config: ProviderConfig {
                client_id,
                client_secret,
                auth_url: "https://github.com/login/oauth/authorize".to_string(),
                token_url: "https://github.com/login/oauth/access_token".to_string(),
                user_info_url: "https://api.github.com/user".to_string(),
                scopes,
                supports_pkce: false,
                requires_user_agent: true,
                redirect_uris,
                additional_fields,
            },
        }
    }
}

#[async_trait]
impl OAuthProvider for GitHubProvider {
    fn name(&self) -> &str { "github" }
    fn config(&self) -> &ProviderConfig { &self.config }

    fn get_api_headers(&self) -> Vec<(String, String)> {
        vec![("User-Agent".to_string(), "CrabShield-Auth-Service".to_string())]
    }

    async fn parse_user_info(&self, user_data: serde_json::Value, access_token: &str) -> Result<OAuthUserInfo, OAuthError> {
        // GitHub requires a separate call to get emails
        let primary_email = self.get_primary_email(access_token).await?;

        Ok(OAuthUserInfo {
            id: user_data["id"].as_u64().unwrap_or_default().to_string(),
            email: primary_email,
            name: user_data["name"].as_str().map(|s| s.to_string()),
            picture: user_data["avatar_url"].as_str().map(|s| s.to_string()),
            verified_email: Some(true), // GitHub emails are considered verified
        })
    }
}

impl GitHubProvider {
    async fn get_primary_email(&self, access_token: &str) -> Result<Option<String>, OAuthError> {
        let client = reqwest::Client::new();
        let emails_response = client
            .get("https://api.github.com/user/emails")
            .bearer_auth(access_token)
            .header("User-Agent", "CrabShield-Auth-Service")
            .send()
            .await
            .map_err(|e| {
                warn!("Failed to fetch GitHub user emails: {}", e);
                e
            })?;

        if emails_response.status().is_success() {
            match emails_response.json::<Vec<serde_json::Value>>().await {
                Ok(emails) => {
                    Ok(emails.iter()
                        .find(|email| email["primary"].as_bool().unwrap_or(false))
                        .and_then(|email| email["email"].as_str())
                        .map(|s| s.to_string()))
                },
                Err(e) => {
                    warn!("Failed to parse GitHub emails JSON: {}", e);
                    Ok(None)
                }
            }
        } else {
            warn!("GitHub emails request failed with status: {}", emails_response.status());
            Ok(None)
        }
    }
}

/// Microsoft OAuth provider
pub struct MicrosoftProvider {
    config: ProviderConfig,
}

impl MicrosoftProvider {
    pub fn new(client_id: String, client_secret: String) -> Self {
        Self::new_with_config(client_id, client_secret, None, None)
    }

    pub fn new_with_config(
        client_id: String,
        client_secret: String,
        custom_scopes: Option<Vec<String>>,
        redirect_uris: Option<Vec<String>>
    ) -> Self {
        let scopes = custom_scopes.unwrap_or_else(|| {
            vec!["openid".to_string(), "email".to_string(), "profile".to_string()]
        });

        let redirect_uris = redirect_uris.unwrap_or_else(|| {
            vec!["/auth/oauth/microsoft/callback".to_string()]
        });

        Self {
            config: ProviderConfig {
                client_id,
                client_secret,
                auth_url: "https://login.microsoftonline.com/common/oauth2/v2.0/authorize".to_string(),
                token_url: "https://login.microsoftonline.com/common/oauth2/v2.0/token".to_string(),
                user_info_url: "https://graph.microsoft.com/v1.0/me".to_string(),
                scopes,
                supports_pkce: true,
                requires_user_agent: false,
                redirect_uris,
                additional_fields: std::collections::HashMap::new(),
            },
        }
    }
}

#[async_trait]
impl OAuthProvider for MicrosoftProvider {
    fn name(&self) -> &str { "microsoft" }
    fn config(&self) -> &ProviderConfig { &self.config }

    async fn parse_user_info(&self, user_data: serde_json::Value, _access_token: &str) -> Result<OAuthUserInfo, OAuthError> {
        Ok(OAuthUserInfo {
            id: user_data["id"].as_str().unwrap_or_default().to_string(),
            email: user_data["mail"].as_str()
                .or_else(|| user_data["userPrincipalName"].as_str())
                .map(|s| s.to_string()),
            name: user_data["displayName"].as_str().map(|s| s.to_string()),
            picture: None, // Microsoft Graph requires additional API call for photo
            verified_email: Some(true), // Microsoft emails are considered verified
        })
    }
}

pub struct OAuthService {
    pool: PgPool,
    config: OAuthConfig,
    providers: HashMap<String, Box<dyn OAuthProvider>>,
    auth_service: Arc<AuthService>,
}

impl OAuthService {
    pub fn new(pool: PgPool, config: OAuthConfig, auth_service: Arc<AuthService>) -> Self {
        let mut providers: HashMap<String, Box<dyn OAuthProvider>> = HashMap::new();

        // Initialize OAuth providers with enhanced configuration
        if !config.google.client_id.is_empty() && config.google.enabled.unwrap_or(true) {
            let provider = GoogleProvider::new_with_config(
                config.google.client_id.clone(),
                config.google.client_secret.clone(),
                config.google.scopes.clone(),
                config.google.redirect_uris.clone(),
            );
            providers.insert("google".to_string(), Box::new(provider));
        }

        if !config.github.client_id.is_empty() && config.github.enabled.unwrap_or(true) {
            let provider = GitHubProvider::new_with_config(
                config.github.client_id.clone(),
                config.github.client_secret.clone(),
                config.github.scopes.clone(),
                config.github.redirect_uris.clone(),
            );
            providers.insert("github".to_string(), Box::new(provider));
        }

        if !config.microsoft.client_id.is_empty() && config.microsoft.enabled.unwrap_or(true) {
            let provider = MicrosoftProvider::new_with_config(
                config.microsoft.client_id.clone(),
                config.microsoft.client_secret.clone(),
                config.microsoft.scopes.clone(),
                config.microsoft.redirect_uris.clone(),
            );
            providers.insert("microsoft".to_string(), Box::new(provider));
        }

        Self {
            pool,
            config,
            providers,
            auth_service,
        }
    }

    /// Initiate OAuth flow
    pub async fn initiate_oauth_flow(
        &self,
        request: OAuthInitiateRequest,
    ) -> Result<OAuthInitiateResponse, OAuthError> {
        info!("Initiating OAuth flow for provider: {}", request.provider);

        // Validate provider
        let provider = self.providers.get(&request.provider)
            .ok_or_else(|| {
                warn!("Invalid OAuth provider requested: {}", request.provider);
                OAuthError::InvalidProvider(request.provider.clone())
            })?;

        // Generate state and code verifier for PKCE
        let state = generate_state();
        let code_verifier = generate_code_verifier();
        debug!("Generated OAuth state and PKCE verifier for provider: {}", request.provider);

        // Validate and construct redirect URI
        let redirect_uri = if let Some(provided_uri) = request.redirect_uri {
            // Validate that the provided redirect URI is allowed for this provider
            if !provider.config().redirect_uris.iter().any(|allowed| {
                provided_uri.ends_with(allowed) || allowed == "*"
            }) {
                warn!("Invalid redirect URI provided for provider {}: {}", request.provider, provided_uri);
                return Err(OAuthError::InvalidRedirectUri);
            }
            provided_uri
        } else {
            // Use default redirect URI
            format!("{}/auth/oauth/{}/callback", self.config.base_url, request.provider)
        };

        if let Err(e) = self.store_oauth_state(&state, Some(&code_verifier), &redirect_uri, &request.provider).await {
            error!("Failed to store OAuth state for provider {}: {}", request.provider, e);
            return Err(e);
        }

        // Generate authorization URL
        let authorization_url = provider.get_auth_url(&state, Some(&code_verifier))?;

        info!("Successfully initiated OAuth flow for provider: {}", request.provider);
        Ok(OAuthInitiateResponse {
            success: true,
            authorization_url,
            state,
            code_verifier: Some(code_verifier),
        })
    }

    /// Handle OAuth callback
    pub async fn handle_oauth_callback(
        &self,
        request: OAuthCallbackRequest,
    ) -> Result<OAuthCallbackResponse, OAuthError> {
        info!("Handling OAuth callback for provider: {}", request.provider);

        // Validate provider
        let provider = self.providers.get(&request.provider)
            .ok_or_else(|| {
                warn!("Invalid OAuth provider in callback: {}", request.provider);
                OAuthError::InvalidProvider(request.provider.clone())
            })?;

        // Validate state
        let oauth_state = self.get_oauth_state(&request.state).await
            .map_err(|e| {
                warn!("Invalid OAuth state in callback: {}", request.state);
                e
            })?;
        if oauth_state.provider != request.provider {
            warn!("OAuth state provider mismatch: expected {}, got {}", oauth_state.provider, request.provider);
            return Err(OAuthError::InvalidState);
        }

        // Exchange code for tokens
        let token_response = provider.exchange_code(&request.code, oauth_state.code_verifier.as_deref()).await?;

        // Get user info from provider
        let user_info = provider.get_user_info(&token_response.access_token).await?;

        // Check if OAuth account already exists
        if let Ok(oauth_account) = self.get_oauth_account_by_provider(&request.provider, &user_info.id).await {
            // Existing OAuth account - update tokens and return user
            self.update_oauth_account_tokens(
                oauth_account.id,
                &token_response.access_token,
                token_response.refresh_token.as_deref(),
                token_response.expires_in,
            ).await?;

            // Clean up state
            self.delete_oauth_state(&request.state).await?;

            return Ok(OAuthCallbackResponse {
                success: true,
                user_id: Some(oauth_account.user_id),
                access_token: Some(token_response.access_token),
                refresh_token: token_response.refresh_token,
                is_new_user: false,
                message: "Successfully authenticated with existing account".to_string(),
            });
        }

        // Check if user exists by email
        let existing_user = if let Some(email) = &user_info.email {
            self.auth_service.get_user_by_email(email).await.unwrap_or_else(|_| None)
        } else {
            None
        };

        let (user_id, is_new_user) = if let Some(user) = existing_user {
            // Link OAuth account to existing user
            (user.id, false)
        } else {
            // Create new user
            let email = user_info.email.clone().ok_or_else(|| OAuthError::ProviderError("Email not provided by OAuth provider".to_string()))?;
            let username = format!("{}_{}", request.provider, &user_info.id);

            let create_user_request = crate::models::CreateUserRequest {
                username,
                email: email.clone(),
                password: generate_random_password(), // Generate random password for OAuth users
                first_name: user_info.name.clone(),
                last_name: None, // OAuth providers typically don't separate first/last names
            };

            let user = self.auth_service.create_user(create_user_request).await
                .map_err(|e| OAuthError::ProviderError(format!("Failed to create user: {}", e)))?;

            (user.id, true)
        };

        // Create OAuth account
        let _oauth_account = self.create_oauth_account(
            user_id,
            &request.provider,
            &user_info.id,
            user_info.email.as_deref(),
            &token_response.access_token,
            token_response.refresh_token.as_deref(),
            token_response.expires_in,
        ).await
        .map_err(|e| {
            error!("Failed to create OAuth account for user {}: {}", user_id, e);
            e
        })?;

        // Clean up state
        if let Err(e) = self.delete_oauth_state(&request.state).await {
            warn!("Failed to clean up OAuth state {}: {}", request.state, e);
        }

        info!("Successfully completed OAuth callback for user {} with provider {}", user_id, request.provider);
        Ok(OAuthCallbackResponse {
            success: true,
            user_id: Some(user_id),
            access_token: Some(token_response.access_token),
            refresh_token: token_response.refresh_token,
            is_new_user,
            message: if is_new_user {
                "Successfully created new account and authenticated".to_string()
            } else {
                "Successfully linked OAuth account and authenticated".to_string()
            },
        })
    }

    /// Link OAuth account to existing user
    pub async fn link_oauth_account(
        &self,
        request: OAuthLinkRequest,
    ) -> Result<OAuthLinkResponse, OAuthError> {
        info!("Linking OAuth account for provider: {} to user: {}", request.provider, request.user_id);

        // Validate provider
        let provider = self.providers.get(&request.provider)
            .ok_or_else(|| {
                warn!("Invalid OAuth provider for linking: {}", request.provider);
                OAuthError::InvalidProvider(request.provider.clone())
            })?;

        // Validate state
        let oauth_state = self.get_oauth_state(&request.state).await
            .map_err(|e| {
                warn!("Invalid OAuth state for linking: {}", request.state);
                e
            })?;
        if oauth_state.provider != request.provider {
            warn!("OAuth state provider mismatch for linking: expected {}, got {}", oauth_state.provider, request.provider);
            return Err(OAuthError::InvalidState);
        }

        // Exchange code for tokens
        let token_response = provider.exchange_code(&request.code, oauth_state.code_verifier.as_deref()).await?;

        // Get user info from provider
        let user_info = provider.get_user_info(&token_response.access_token).await?;

        // Check if OAuth account already exists
        if self.get_oauth_account_by_provider(&request.provider, &user_info.id).await.is_ok() {
            warn!("Attempted to link already linked OAuth account: provider={}, user_id={}", request.provider, request.user_id);
            return Err(OAuthError::AccountAlreadyLinked);
        }

        // Create OAuth account
        let oauth_account = self.create_oauth_account(
            request.user_id,
            &request.provider,
            &user_info.id,
            user_info.email.as_deref(),
            &token_response.access_token,
            token_response.refresh_token.as_deref(),
            token_response.expires_in,
        ).await
        .map_err(|e| {
            error!("Failed to create OAuth account for linking user {}: {}", request.user_id, e);
            e
        })?;

        // Clean up state
        if let Err(e) = self.delete_oauth_state(&request.state).await {
            warn!("Failed to clean up OAuth state {}: {}", request.state, e);
        }

        info!("Successfully linked OAuth account for user {} with provider {}", request.user_id, request.provider);
        Ok(OAuthLinkResponse {
            success: true,
            message: "OAuth account successfully linked".to_string(),
            oauth_account_id: Some(oauth_account.id),
        })
    }

    /// Unlink OAuth account
    pub async fn unlink_oauth_account(
        &self,
        request: OAuthUnlinkRequest,
    ) -> Result<OAuthUnlinkResponse, OAuthError> {
        info!("Unlinking OAuth account for provider: {} from user: {}", request.provider, request.user_id);

        // Find and delete OAuth account
        let result = sqlx::query(
            "DELETE FROM oauth_accounts WHERE user_id = $1 AND provider = $2"
        )
        .bind(request.user_id)
        .bind(&request.provider)
        .execute(&self.pool)
        .await
        .map_err(|e| {
            error!("Database error while unlinking OAuth account for user {}: {}", request.user_id, e);
            OAuthError::DatabaseError(e)
        })?;

        if result.rows_affected() == 0 {
            warn!("Attempted to unlink non-existent OAuth account: user_id={}, provider={}", request.user_id, request.provider);
            return Err(OAuthError::AccountNotFound);
        }

        info!("Successfully unlinked OAuth account for user {} with provider {}", request.user_id, request.provider);

        Ok(OAuthUnlinkResponse {
            success: true,
            message: "OAuth account successfully unlinked".to_string(),
        })
    }

    /// Get available OAuth providers
    pub async fn get_oauth_providers(&self) -> Result<OAuthProvidersResponse, OAuthError> {
        let providers = self.providers.iter().map(|(name, _provider)| {
            OAuthProviderInfo {
                name: name.clone(),
                display_name: match name.as_str() {
                    "google" => "Google".to_string(),
                    "github" => "GitHub".to_string(),
                    "microsoft" => "Microsoft".to_string(),
                    "apple" => "Apple".to_string(),
                    _ => name.clone(),
                },
                enabled: true,
                authorization_url: format!("{}/auth/oauth/{}", self.config.base_url, name),
            }
        }).collect();

        Ok(OAuthProvidersResponse {
            success: true,
            providers,
        })
    }

    // Private helper methods
    async fn store_oauth_state(
        &self,
        state: &str,
        code_verifier: Option<&str>,
        redirect_uri: &str,
        provider: &str,
    ) -> Result<(), OAuthError> {
        let expires_at = Utc::now() + Duration::minutes(10); // 10-minute expiration

        sqlx::query(
            "INSERT INTO oauth_states (state, code_verifier, redirect_uri, provider, expires_at) VALUES ($1, $2, $3, $4, $5)"
        )
        .bind(state)
        .bind(code_verifier)
        .bind(redirect_uri)
        .bind(provider)
        .bind(expires_at)
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    async fn get_oauth_state(&self, state: &str) -> Result<OAuthState, OAuthError> {
        let row = sqlx::query_as::<_, OAuthState>(
            "SELECT id, state, code_verifier, redirect_uri, provider, expires_at, created_at FROM oauth_states WHERE state = $1 AND expires_at > NOW()"
        )
        .bind(state)
        .fetch_one(&self.pool)
        .await
        .map_err(|_| OAuthError::InvalidState)?;

        Ok(row)
    }

    async fn delete_oauth_state(&self, state: &str) -> Result<(), OAuthError> {
        sqlx::query("DELETE FROM oauth_states WHERE state = $1")
            .bind(state)
            .execute(&self.pool)
            .await?;
        Ok(())
    }

    async fn get_oauth_account_by_provider(
        &self,
        provider: &str,
        provider_user_id: &str,
    ) -> Result<OAuthAccount, OAuthError> {
        let row = sqlx::query_as::<_, OAuthAccount>(
            "SELECT id, user_id, provider, provider_user_id, email, access_token, refresh_token, expires_at, created_at, updated_at FROM oauth_accounts WHERE provider = $1 AND provider_user_id = $2"
        )
        .bind(provider)
        .bind(provider_user_id)
        .fetch_one(&self.pool)
        .await?;

        Ok(row)
    }

    async fn create_oauth_account(
        &self,
        user_id: Uuid,
        provider: &str,
        provider_user_id: &str,
        email: Option<&str>,
        access_token: &str,
        refresh_token: Option<&str>,
        expires_in: Option<i64>,
    ) -> Result<OAuthAccount, OAuthError> {
        let expires_at = expires_in.map(|exp| Utc::now() + Duration::seconds(exp));

        let row = sqlx::query_as::<_, OAuthAccount>(
            r#"
            INSERT INTO oauth_accounts (user_id, provider, provider_user_id, email, access_token, refresh_token, expires_at)
            VALUES ($1, $2, $3, $4, $5, $6, $7)
            RETURNING id, user_id, provider, provider_user_id, email, access_token, refresh_token, expires_at, created_at, updated_at
            "#
        )
        .bind(user_id)
        .bind(provider)
        .bind(provider_user_id)
        .bind(email)
        .bind(access_token)
        .bind(refresh_token)
        .bind(expires_at)
        .fetch_one(&self.pool)
        .await?;

        Ok(row)
    }

    async fn update_oauth_account_tokens(
        &self,
        oauth_account_id: Uuid,
        access_token: &str,
        refresh_token: Option<&str>,
        expires_in: Option<i64>,
    ) -> Result<(), OAuthError> {
        let expires_at = expires_in.map(|exp| Utc::now() + Duration::seconds(exp));

        sqlx::query(
            "UPDATE oauth_accounts SET access_token = $1, refresh_token = $2, expires_at = $3, updated_at = NOW() WHERE id = $4"
        )
        .bind(access_token)
        .bind(refresh_token)
        .bind(expires_at)
        .bind(oauth_account_id)
        .execute(&self.pool)
        .await?;

        Ok(())
    }

}

// Helper function to generate random password for OAuth users
fn generate_random_password() -> String {
    use base64::Engine;
    let mut bytes = [0u8; 32];
    rand::Rng::fill(&mut rand::rng(), &mut bytes);
    base64::engine::general_purpose::STANDARD.encode(bytes)
}


