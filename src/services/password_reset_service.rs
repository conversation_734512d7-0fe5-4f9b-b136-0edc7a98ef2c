use crate::models::User;
use crate::services::{PasswordService, JwtService};

use anyhow::Result;
use chrono::{DateTime, Duration, Utc};
use redis::{Client as RedisClient, Commands, Connection};
use serde::{Deserialize, Serialize};
use sqlx::PgPool;

use thiserror::Error;
use tracing::{error, info};
use uuid::Uuid;
use validator::Validate;

#[derive(Error, Debug)]
pub enum PasswordResetError {
    #[error("Database error: {0}")]
    DatabaseError(String),
    
    #[error("Redis error: {0}")]
    RedisError(String),
    
    #[error("User not found")]
    UserNotFound,
    
    #[error("Invalid reset token")]
    InvalidToken,
    
    #[error("Reset token expired")]
    TokenExpired,
    
    #[error("Reset token already used")]
    TokenAlreadyUsed,
    
    #[error("Rate limit exceeded")]
    RateLimitExceeded,
    
    #[error("Email service error: {0}")]
    EmailServiceError(String),
    
    #[error("Password validation failed: {0}")]
    PasswordValidationFailed(String),
    
    #[error("Internal error: {0}")]
    InternalError(String),
}

#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct PasswordResetRequest {
    #[validate(email(message = "Invalid email format"))]
    pub email: String,
    
    #[validate(length(min = 1, message = "Captcha token is required"))]
    pub captcha_token: Option<String>,
    
    pub client_ip: Option<String>,
    pub user_agent: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct PasswordResetConfirmRequest {
    #[validate(length(min = 1, message = "Reset token is required"))]
    pub reset_token: String,
    
    #[validate(length(min = 8, max = 128, message = "Password must be between 8 and 128 characters"))]
    pub new_password: String,
    
    pub confirm_password: String,
    
    pub client_ip: Option<String>,
    pub user_agent: Option<String>,
}



#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PasswordResetToken {
    pub token_id: String,
    pub user_id: Uuid,
    pub email: String,
    pub created_at: DateTime<Utc>,
    pub expires_at: DateTime<Utc>,
    pub is_used: bool,
    pub client_ip: Option<String>,
    pub user_agent: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PasswordResetResponse {
    pub success: bool,
    pub message: String,
    pub reset_token_sent: bool,
    pub rate_limit_info: Option<RateLimitInfo>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PasswordResetConfirmResponse {
    pub success: bool,
    pub message: String,
    pub password_changed: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RateLimitInfo {
    pub attempts_remaining: u32,
    pub reset_time: DateTime<Utc>,
    pub retry_after_seconds: u64,
}

#[derive(Debug, Clone)]
pub struct PasswordResetPolicy {
    pub token_expiry_duration: Duration,
    pub max_requests_per_hour: u32,
    pub max_requests_per_day: u32,
    pub cleanup_interval: Duration,
    pub require_captcha_after_attempts: u32,
}

impl Default for PasswordResetPolicy {
    fn default() -> Self {
        Self {
            token_expiry_duration: Duration::hours(1),
            max_requests_per_hour: 3,
            max_requests_per_day: 10,
            cleanup_interval: Duration::hours(6),
            require_captcha_after_attempts: 2,
        }
    }
}

pub struct PasswordResetService {
    db_pool: PgPool,
    redis_client: RedisClient,
    password_service: PasswordService,
    jwt_service: JwtService,
    policy: PasswordResetPolicy,
    email_service_url: String,
}

impl PasswordResetService {
    pub fn new(
        db_pool: PgPool,
        redis_client: RedisClient,
        password_service: PasswordService,
        jwt_service: JwtService,
        email_service_url: String,
    ) -> Self {
        Self {
            db_pool,
            redis_client,
            password_service,
            jwt_service,
            policy: PasswordResetPolicy::default(),
            email_service_url,
        }
    }

    pub fn with_policy(mut self, policy: PasswordResetPolicy) -> Self {
        self.policy = policy;
        self
    }

    /// Initiate password reset process
    pub async fn initiate_password_reset(
        &self,
        request: PasswordResetRequest,
    ) -> Result<PasswordResetResponse, PasswordResetError> {
        // Validate request
        request.validate()
            .map_err(|e| PasswordResetError::PasswordValidationFailed(e.to_string()))?;

        let client_ip = request.client_ip.as_deref().unwrap_or("unknown");
        
        // Check rate limits
        if let Some(rate_limit_info) = self.check_rate_limits(&request.email, client_ip).await? {
            return Ok(PasswordResetResponse {
                success: false,
                message: "Rate limit exceeded. Please try again later.".to_string(),
                reset_token_sent: false,
                rate_limit_info: Some(rate_limit_info),
            });
        }

        // Find user by email
        let user = match self.find_user_by_email(&request.email).await? {
            Some(user) => user,
            None => {
                // For security, don't reveal if email exists
                // But still record the attempt for rate limiting
                self.record_reset_attempt(&request.email, client_ip, false).await?;
                
                return Ok(PasswordResetResponse {
                    success: true,
                    message: "If the email exists, a password reset link has been sent.".to_string(),
                    reset_token_sent: false,
                    rate_limit_info: None,
                });
            }
        };

        // Generate secure reset token
        let reset_token = self.generate_reset_token(&user, &request).await?;
        
        // Send reset email
        match self.send_reset_email(&user, &reset_token).await {
            Ok(_) => {
                self.record_reset_attempt(&request.email, client_ip, true).await?;
                
                info!(
                    user_id = %user.id,
                    email = %user.email,
                    client_ip = %client_ip,
                    "Password reset email sent successfully"
                );

                Ok(PasswordResetResponse {
                    success: true,
                    message: "If the email exists, a password reset link has been sent.".to_string(),
                    reset_token_sent: true,
                    rate_limit_info: None,
                })
            }
            Err(e) => {
                error!(
                    user_id = %user.id,
                    email = %user.email,
                    error = %e,
                    "Failed to send password reset email"
                );

                Err(PasswordResetError::EmailServiceError(e.to_string()))
            }
        }
    }

    /// Confirm password reset with new password
    pub async fn confirm_password_reset(
        &self,
        request: PasswordResetConfirmRequest,
    ) -> Result<PasswordResetConfirmResponse, PasswordResetError> {
        // Validate request
        request.validate()
            .map_err(|e| PasswordResetError::PasswordValidationFailed(e.to_string()))?;

        // Check password confirmation
        if request.new_password != request.confirm_password {
            return Err(PasswordResetError::PasswordValidationFailed(
                "Passwords do not match".to_string()
            ));
        }

        // Validate and consume reset token
        let reset_token = self.validate_and_consume_token(&request.reset_token).await?;
        
        // Validate new password strength
        self.password_service
            .validate_password(&request.new_password, &Default::default())
            .map_err(|e| PasswordResetError::PasswordValidationFailed(e.to_string()))?;

        // Hash new password
        let password_hash = self.password_service
            .hash_password(&request.new_password)
            .map_err(|e| PasswordResetError::PasswordValidationFailed(e.to_string()))?;

        // Update password in database
        self.update_user_password(reset_token.user_id, &password_hash).await?;
        
        // Log password change event
        self.log_password_change_event(&reset_token, &request).await?;

        info!(
            user_id = %reset_token.user_id,
            email = %reset_token.email,
            "Password reset completed successfully"
        );

        Ok(PasswordResetConfirmResponse {
            success: true,
            message: "Password has been reset successfully.".to_string(),
            password_changed: true,
        })
    }

    /// Validate reset token without consuming it
    pub async fn validate_reset_token(&self, token: &str) -> Result<PasswordResetToken, PasswordResetError> {
        let mut conn = self.get_redis_connection()?;
        let token_key = format!("password_reset_token:{}", token);

        let token_data: String = conn.get(&token_key)
            .map_err(|e| PasswordResetError::RedisError(e.to_string()))?;

        let reset_token: PasswordResetToken = serde_json::from_str(&token_data)
            .map_err(|_| PasswordResetError::InvalidToken)?;

        // Check if token is expired
        if Utc::now() > reset_token.expires_at {
            return Err(PasswordResetError::TokenExpired);
        }

        // Check if token is already used
        if reset_token.is_used {
            return Err(PasswordResetError::TokenAlreadyUsed);
        }

        Ok(reset_token)
    }

    /// Clean up expired tokens (should be called periodically)
    pub async fn cleanup_expired_tokens(&self) -> Result<u32, PasswordResetError> {
        let mut conn = self.get_redis_connection()?;
        let pattern = "password_reset_token:*";

        let keys: Vec<String> = conn.keys(pattern)
            .map_err(|e| PasswordResetError::RedisError(e.to_string()))?;

        let mut cleaned_count = 0;
        let now = Utc::now();

        for key in keys {
            if let Ok(token_data) = conn.get::<_, String>(&key) {
                if let Ok(reset_token) = serde_json::from_str::<PasswordResetToken>(&token_data) {
                    if now > reset_token.expires_at {
                        let _: () = conn.del(&key)
                            .map_err(|e| PasswordResetError::RedisError(e.to_string()))?;
                        cleaned_count += 1;
                    }
                }
            }
        }

        info!(cleaned_count = cleaned_count, "Cleaned up expired password reset tokens");
        Ok(cleaned_count)
    }

    // Private helper methods

    /// Get Redis connection
    fn get_redis_connection(&self) -> Result<Connection, PasswordResetError> {
        self.redis_client
            .get_connection()
            .map_err(|e| PasswordResetError::RedisError(e.to_string()))
    }

    /// Check rate limits for password reset requests
    async fn check_rate_limits(
        &self,
        email: &str,
        client_ip: &str,
    ) -> Result<Option<RateLimitInfo>, PasswordResetError> {
        let mut conn = self.get_redis_connection()?;
        let now = Utc::now();

        // Check hourly rate limit
        let hourly_key = format!("password_reset_rate:hourly:{}:{}", email, now.format("%Y%m%d%H"));
        let daily_key = format!("password_reset_rate:daily:{}:{}", email, now.format("%Y%m%d"));
        let ip_hourly_key = format!("password_reset_rate:ip_hourly:{}:{}", client_ip, now.format("%Y%m%d%H"));

        let hourly_count: u32 = conn.get(&hourly_key).unwrap_or(0);
        let daily_count: u32 = conn.get(&daily_key).unwrap_or(0);
        let ip_hourly_count: u32 = conn.get(&ip_hourly_key).unwrap_or(0);

        // Check if any limits are exceeded
        if hourly_count >= self.policy.max_requests_per_hour {
            let reset_time = now + Duration::hours(1);
            return Ok(Some(RateLimitInfo {
                attempts_remaining: 0,
                reset_time,
                retry_after_seconds: 3600,
            }));
        }

        if daily_count >= self.policy.max_requests_per_day {
            let reset_time = now + Duration::days(1);
            return Ok(Some(RateLimitInfo {
                attempts_remaining: 0,
                reset_time,
                retry_after_seconds: 86400,
            }));
        }

        if ip_hourly_count >= self.policy.max_requests_per_hour * 3 {
            let reset_time = now + Duration::hours(1);
            return Ok(Some(RateLimitInfo {
                attempts_remaining: 0,
                reset_time,
                retry_after_seconds: 3600,
            }));
        }

        Ok(None)
    }

    /// Record a password reset attempt
    async fn record_reset_attempt(
        &self,
        email: &str,
        client_ip: &str,
        success: bool,
    ) -> Result<(), PasswordResetError> {
        let mut conn = self.get_redis_connection()?;
        let now = Utc::now();

        let hourly_key = format!("password_reset_rate:hourly:{}:{}", email, now.format("%Y%m%d%H"));
        let daily_key = format!("password_reset_rate:daily:{}:{}", email, now.format("%Y%m%d"));
        let ip_hourly_key = format!("password_reset_rate:ip_hourly:{}:{}", client_ip, now.format("%Y%m%d%H"));

        // Increment counters
        let _: () = conn.incr(&hourly_key, 1)
            .map_err(|e| PasswordResetError::RedisError(e.to_string()))?;
        let _: () = conn.expire(&hourly_key, 3600)
            .map_err(|e| PasswordResetError::RedisError(e.to_string()))?;

        let _: () = conn.incr(&daily_key, 1)
            .map_err(|e| PasswordResetError::RedisError(e.to_string()))?;
        let _: () = conn.expire(&daily_key, 86400)
            .map_err(|e| PasswordResetError::RedisError(e.to_string()))?;

        let _: () = conn.incr(&ip_hourly_key, 1)
            .map_err(|e| PasswordResetError::RedisError(e.to_string()))?;
        let _: () = conn.expire(&ip_hourly_key, 3600)
            .map_err(|e| PasswordResetError::RedisError(e.to_string()))?;

        // Log to database for audit
        self.log_reset_attempt_to_db(email, client_ip, success).await?;

        Ok(())
    }

    /// Find user by email
    async fn find_user_by_email(&self, email: &str) -> Result<Option<User>, PasswordResetError> {
        let query = "SELECT * FROM users WHERE email = $1 AND is_active = true";

        match sqlx::query_as::<_, User>(query)
            .bind(email)
            .fetch_optional(&self.db_pool)
            .await
        {
            Ok(user) => Ok(user),
            Err(e) => {
                error!(email = %email, error = %e, "Database error finding user");
                Err(PasswordResetError::DatabaseError(e.to_string()))
            }
        }
    }

    /// Generate secure reset token
    async fn generate_reset_token(
        &self,
        user: &User,
        request: &PasswordResetRequest,
    ) -> Result<String, PasswordResetError> {
        let token_id = Uuid::new_v4().to_string();
        let now = Utc::now();
        let expires_at = now + self.policy.token_expiry_duration;

        let reset_token = PasswordResetToken {
            token_id: token_id.clone(),
            user_id: user.id,
            email: user.email.clone(),
            created_at: now,
            expires_at,
            is_used: false,
            client_ip: request.client_ip.clone(),
            user_agent: request.user_agent.clone(),
        };

        // Store token in Redis
        let mut conn = self.get_redis_connection()?;
        let token_key = format!("password_reset_token:{}", token_id);
        let token_data = serde_json::to_string(&reset_token)
            .map_err(|e| PasswordResetError::InternalError(e.to_string()))?;

        let _: () = conn.set_ex(
            &token_key,
            token_data,
            self.policy.token_expiry_duration.num_seconds() as u64,
        ).map_err(|e| PasswordResetError::RedisError(e.to_string()))?;

        // Also create a JWT token for additional security
        let jwt_token = self.jwt_service
            .create_special_token(
                user.id,
                user.email.clone(),
                crate::services::jwt_service::TokenType::PasswordReset,
                self.policy.token_expiry_duration,
            )
            .map_err(|e| PasswordResetError::InternalError(e.to_string()))?;

        Ok(jwt_token)
    }

    /// Send password reset email
    async fn send_reset_email(
        &self,
        user: &User,
        reset_token: &str,
    ) -> Result<(), PasswordResetError> {
        let reset_url = format!("https://app.crabshield.com/reset-password?token={}", reset_token);

        let email_payload = serde_json::json!({
            "to": user.email,
            "subject": "Password Reset Request - CrabShield",
            "template": "password_reset",
            "data": {
                "user_name": user.first_name.as_deref().unwrap_or("User"),
                "reset_url": reset_url,
                "expires_in_hours": self.policy.token_expiry_duration.num_hours(),
                "support_email": "<EMAIL>"
            }
        });

        let client = reqwest::Client::new();
        let response = client
            .post(&format!("{}/api/v1/email/send", self.email_service_url))
            .json(&email_payload)
            .send()
            .await
            .map_err(|e| PasswordResetError::EmailServiceError(e.to_string()))?;

        if !response.status().is_success() {
            let error_text = response.text().await.unwrap_or_else(|_| "Unknown error".to_string());
            return Err(PasswordResetError::EmailServiceError(error_text));
        }

        Ok(())
    }

    /// Validate and consume reset token
    async fn validate_and_consume_token(&self, token: &str) -> Result<PasswordResetToken, PasswordResetError> {
        // First validate the JWT token
        let claims = self.jwt_service
            .validate_token(token)
            .map_err(|_| PasswordResetError::InvalidToken)?;

        // Ensure it's a password reset token
        if !matches!(claims.token_type, crate::services::jwt_service::TokenType::PasswordReset) {
            return Err(PasswordResetError::InvalidToken);
        }

        // Get the token from Redis using the JWT ID
        let mut conn = self.get_redis_connection()?;
        let token_key = format!("password_reset_token:{}", claims.jti);

        let token_data: String = conn.get(&token_key)
            .map_err(|_| PasswordResetError::InvalidToken)?;

        let mut reset_token: PasswordResetToken = serde_json::from_str(&token_data)
            .map_err(|_| PasswordResetError::InvalidToken)?;

        // Check if token is expired
        if Utc::now() > reset_token.expires_at {
            return Err(PasswordResetError::TokenExpired);
        }

        // Check if token is already used
        if reset_token.is_used {
            return Err(PasswordResetError::TokenAlreadyUsed);
        }

        // Mark token as used
        reset_token.is_used = true;
        let updated_token_data = serde_json::to_string(&reset_token)
            .map_err(|e| PasswordResetError::InternalError(e.to_string()))?;

        let _: () = conn.set_ex(
            &token_key,
            updated_token_data,
            self.policy.token_expiry_duration.num_seconds() as u64,
        ).map_err(|e| PasswordResetError::RedisError(e.to_string()))?;

        Ok(reset_token)
    }

    /// Update user password in database
    async fn update_user_password(
        &self,
        user_id: Uuid,
        password_hash: &str,
    ) -> Result<(), PasswordResetError> {
        let query = r#"
            UPDATE users
            SET password_hash = $1,
                password_changed_at = NOW(),
                updated_at = NOW()
            WHERE id = $2
        "#;

        sqlx::query(query)
            .bind(password_hash)
            .bind(user_id)
            .execute(&self.db_pool)
            .await
            .map_err(|e| PasswordResetError::DatabaseError(e.to_string()))?;

        Ok(())
    }

    /// Log password change event for audit
    async fn log_password_change_event(
        &self,
        reset_token: &PasswordResetToken,
        request: &PasswordResetConfirmRequest,
    ) -> Result<(), PasswordResetError> {
        let query = r#"
            INSERT INTO audit_logs (
                user_id, event_type, event_data, ip_address, user_agent, created_at
            ) VALUES ($1, $2, $3, $4, $5, NOW())
        "#;

        let event_data = serde_json::json!({
            "event": "password_reset_completed",
            "reset_token_id": reset_token.token_id,
            "reset_initiated_at": reset_token.created_at,
            "method": "password_reset_token"
        });

        sqlx::query(query)
            .bind(reset_token.user_id)
            .bind("password_reset")
            .bind(event_data)
            .bind(request.client_ip.as_deref())
            .bind(request.user_agent.as_deref())
            .execute(&self.db_pool)
            .await
            .map_err(|e| PasswordResetError::DatabaseError(e.to_string()))?;

        Ok(())
    }

    /// Log password reset attempt to database
    async fn log_reset_attempt_to_db(
        &self,
        email: &str,
        client_ip: &str,
        success: bool,
    ) -> Result<(), PasswordResetError> {
        let query = r#"
            INSERT INTO audit_logs (
                event_type, event_data, ip_address, created_at
            ) VALUES ($1, $2, $3, NOW())
        "#;

        let event_data = serde_json::json!({
            "event": "password_reset_attempt",
            "email": email,
            "success": success,
            "timestamp": Utc::now()
        });

        sqlx::query(query)
            .bind("password_reset_attempt")
            .bind(event_data)
            .bind(client_ip)
            .execute(&self.db_pool)
            .await
            .map_err(|e| PasswordResetError::DatabaseError(e.to_string()))?;

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    // Helper function to create test password reset service
    async fn create_test_service() -> PasswordResetService {
        let database_url = std::env::var("DATABASE_URL")
            .unwrap_or_else(|_| "postgresql://admin:secret@localhost:5432/crabshield_db".to_string());
        let pool = PgPool::connect(&database_url).await.unwrap();

        let redis_client = redis::Client::open("redis://localhost:6379").unwrap();
        let password_service = crate::services::PasswordService::new_with_db(pool.clone());
        let jwt_service = crate::services::JwtService::new(
            "test_secret_key_for_testing_only",
            "crabshield-auth".to_string(),
            "crabshield-api".to_string(),
        );

        PasswordResetService::new(
            pool,
            redis_client,
            password_service,
            jwt_service,
            "http://localhost:3001".to_string()
        )
    }

    #[test]
    fn test_password_reset_policy_defaults() {
        let policy = PasswordResetPolicy::default();
        assert_eq!(policy.token_expiry_duration, Duration::hours(1));
        assert_eq!(policy.max_requests_per_hour, 3);
        assert_eq!(policy.max_requests_per_day, 10);
        assert_eq!(policy.cleanup_interval, Duration::hours(6));
    }

    #[test]
    fn test_password_reset_request_validation() {
        // Valid request
        let valid_request = PasswordResetRequest {
            email: "<EMAIL>".to_string(),
            captcha_token: Some("valid_captcha".to_string()),
            client_ip: Some("***********".to_string()),
            user_agent: Some("Mozilla/5.0".to_string()),
        };
        assert!(valid_request.validate().is_ok());

        // Invalid request - empty email
        let invalid_request = PasswordResetRequest {
            email: "".to_string(),
            captcha_token: Some("valid_captcha".to_string()),
            client_ip: Some("***********".to_string()),
            user_agent: Some("Mozilla/5.0".to_string()),
        };
        assert!(invalid_request.validate().is_err());

        // Invalid request - invalid email format
        let invalid_email_request = PasswordResetRequest {
            email: "invalid-email".to_string(),
            captcha_token: Some("valid_captcha".to_string()),
            client_ip: Some("***********".to_string()),
            user_agent: Some("Mozilla/5.0".to_string()),
        };
        assert!(invalid_email_request.validate().is_err());
    }

    #[test]
    fn test_confirm_password_reset_request_validation() {
        // Valid request
        let valid_request = PasswordResetConfirmRequest {
            reset_token: "valid_token_123".to_string(),
            new_password: "NewSecurePassword123!".to_string(),
            confirm_password: "NewSecurePassword123!".to_string(),
            client_ip: Some("***********".to_string()),
            user_agent: Some("Mozilla/5.0".to_string()),
        };
        assert!(valid_request.validate().is_ok());

        // Invalid request - empty token
        let invalid_request = PasswordResetConfirmRequest {
            reset_token: "".to_string(),
            new_password: "NewSecurePassword123!".to_string(),
            confirm_password: "NewSecurePassword123!".to_string(),
            client_ip: Some("***********".to_string()),
            user_agent: Some("Mozilla/5.0".to_string()),
        };
        assert!(invalid_request.validate().is_err());

        // Invalid request - weak password
        let weak_password_request = PasswordResetConfirmRequest {
            reset_token: "valid_token_123".to_string(),
            new_password: "123".to_string(),
            confirm_password: "123".to_string(),
            client_ip: Some("***********".to_string()),
            user_agent: Some("Mozilla/5.0".to_string()),
        };
        assert!(weak_password_request.validate().is_err());
    }

    #[tokio::test]
    async fn test_token_generation() {
        let service = create_test_service().await;

        // Create a mock user for testing
        let user = User {
            id: uuid::Uuid::new_v4(),
            email: "<EMAIL>".to_string(),
            first_name: Some("Test".to_string()),
            last_name: Some("User".to_string()),
            password_hash: "dummy_hash".to_string(),
            is_active: true,
            is_verified: true,
            created_at: chrono::Utc::now(),
            updated_at: chrono::Utc::now(),
            last_login_at: None,
            password_changed_at: None,
        };

        let request = PasswordResetRequest {
            email: "<EMAIL>".to_string(),
            captcha_token: Some("test_captcha".to_string()),
            client_ip: Some("127.0.0.1".to_string()),
            user_agent: Some("test_agent".to_string()),
        };

        let token1 = service.generate_reset_token(&user, &request).await.unwrap();
        let token2 = service.generate_reset_token(&user, &request).await.unwrap();

        // Tokens should be different (JWT tokens have unique JTI)
        assert_ne!(token1, token2);

        // Tokens should be valid JWT format
        assert!(token1.len() > 50); // JWT tokens are typically much longer
        assert!(token2.len() > 50);
    }

    #[test]
    fn test_rate_limit_info() {
        let reset_time = Utc::now() + Duration::hours(1);
        let rate_limit = RateLimitInfo {
            attempts_remaining: 0,
            reset_time,
            retry_after_seconds: 3600,
        };

        assert_eq!(rate_limit.attempts_remaining, 0);
        assert_eq!(rate_limit.retry_after_seconds, 3600);
        assert!(rate_limit.reset_time > Utc::now());
    }

    #[test]
    fn test_password_reset_response() {
        let response = PasswordResetResponse {
            success: true,
            message: "Password reset email sent".to_string(),
            reset_token_sent: true,
            rate_limit_info: None,
        };

        assert!(response.success);
        assert!(response.reset_token_sent);
        assert!(response.rate_limit_info.is_none());
    }

    #[test]
    fn test_confirm_password_reset_response() {
        let response = ConfirmPasswordResetResponse {
            success: true,
            message: "Password reset successfully".to_string(),
        };

        assert!(response.success);
        assert_eq!(response.message, "Password reset successfully");
    }

    #[tokio::test]
    async fn test_cleanup_expired_tokens() {
        let service = create_test_service().await;

        // This test would require setting up expired tokens in the database
        // For now, we'll just test that the method doesn't panic
        let result = service.cleanup_expired_tokens().await;
        assert!(result.is_ok());
    }
}
