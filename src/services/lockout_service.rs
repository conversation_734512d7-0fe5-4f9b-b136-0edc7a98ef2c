use anyhow::Result;
use thiserror::Error;
use uuid::Uuid;
use chrono::{DateTime, Utc, Duration};
use redis::{Client as RedisClient, Commands, Connection};
use sqlx::PgPool;

use serde::{Serialize, Deserialize};

#[derive(Error, Debug)]
pub enum LockoutError {
    #[error("Account is locked: {0}")]
    AccountLocked(String),
    #[error("IP address is rate limited: {0}")]
    IpRateLimited(String),
    #[error("Too many failed attempts: {0}")]
    TooManyAttempts(String),
    #[error("Redis error: {0}")]
    RedisError(String),
    #[error("Database error: {0}")]
    DatabaseError(String),
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LockoutInfo {
    pub is_locked: bool,
    pub locked_until: Option<DateTime<Utc>>,
    pub failed_attempts: u32,
    pub lockout_reason: Option<String>,
    pub next_attempt_allowed_at: Option<DateTime<Utc>>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct RateLimitInfo {
    pub is_rate_limited: bool,
    pub attempts_remaining: u32,
    pub reset_time: DateTime<Utc>,
    pub retry_after_seconds: u64,
}

#[derive(Debug, Clone)]
pub struct LockoutPolicy {
    pub max_failed_attempts: u32,
    pub lockout_durations: Vec<Duration>, // Progressive lockout durations
    pub rate_limit_window: Duration,
    pub rate_limit_max_attempts: u32,
    pub ip_rate_limit_window: Duration,
    pub ip_rate_limit_max_attempts: u32,
}

impl Default for LockoutPolicy {
    fn default() -> Self {
        Self {
            max_failed_attempts: 5,
            lockout_durations: vec![
                Duration::minutes(5),   // First lockout: 5 minutes
                Duration::minutes(15),  // Second lockout: 15 minutes
                Duration::hours(1),     // Third lockout: 1 hour
                Duration::hours(4),     // Fourth lockout: 4 hours
                Duration::hours(24),    // Fifth+ lockout: 24 hours
            ],
            rate_limit_window: Duration::minutes(15),
            rate_limit_max_attempts: 10,
            ip_rate_limit_window: Duration::minutes(15),
            ip_rate_limit_max_attempts: 50,
        }
    }
}

#[derive(Clone)]
pub struct LockoutService {
    redis_client: RedisClient,
    db_pool: PgPool,
    policy: LockoutPolicy,
}

impl LockoutService {
    pub fn new(redis_url: &str, db_pool: PgPool) -> Result<Self, LockoutError> {
        let redis_client = RedisClient::open(redis_url)
            .map_err(|e| LockoutError::RedisError(e.to_string()))?;

        Ok(Self {
            redis_client,
            db_pool,
            policy: LockoutPolicy::default(),
        })
    }

    pub fn with_policy(mut self, policy: LockoutPolicy) -> Self {
        self.policy = policy;
        self
    }

    /// Check if an account is currently locked
    pub async fn check_account_lockout(&self, user_id: Uuid) -> Result<LockoutInfo, LockoutError> {
        let mut conn = self.get_redis_connection()?;
        
        let lockout_key = format!("lockout:user:{}", user_id);
        let attempts_key = format!("failed_attempts:user:{}", user_id);

        // Check if account is currently locked
        let locked_until: Option<String> = conn.get(&lockout_key)
            .map_err(|e| LockoutError::RedisError(e.to_string()))?;

        if let Some(locked_until_str) = locked_until {
            let locked_until = DateTime::parse_from_rfc3339(&locked_until_str)
                .map_err(|e| LockoutError::RedisError(e.to_string()))?
                .with_timezone(&Utc);

            if locked_until > Utc::now() {
                let failed_attempts: u32 = conn.get(&attempts_key).unwrap_or(0);
                
                return Ok(LockoutInfo {
                    is_locked: true,
                    locked_until: Some(locked_until),
                    failed_attempts,
                    lockout_reason: Some("Too many failed login attempts".to_string()),
                    next_attempt_allowed_at: Some(locked_until),
                });
            } else {
                // Lockout has expired, clean up
                let _: () = conn.del(&lockout_key)
                    .map_err(|e| LockoutError::RedisError(e.to_string()))?;
                let _: () = conn.del(&attempts_key)
                    .map_err(|e| LockoutError::RedisError(e.to_string()))?;
            }
        }

        // Get current failed attempts count
        let failed_attempts: u32 = conn.get(&attempts_key).unwrap_or(0);

        Ok(LockoutInfo {
            is_locked: false,
            locked_until: None,
            failed_attempts,
            lockout_reason: None,
            next_attempt_allowed_at: None,
        })
    }

    /// Check IP-based rate limiting
    pub async fn check_ip_rate_limit(&self, ip_address: &str) -> Result<RateLimitInfo, LockoutError> {
        let mut conn = self.get_redis_connection()?;
        
        let rate_limit_key = format!("rate_limit:ip:{}", ip_address);
        let window_start = Utc::now() - self.policy.ip_rate_limit_window;
        let window_start_timestamp = window_start.timestamp();

        // Remove old entries outside the window
        let _: () = conn.zrembyscore(&rate_limit_key, 0, window_start_timestamp)
            .map_err(|e| LockoutError::RedisError(e.to_string()))?;

        // Count current attempts in the window
        let current_attempts: u32 = conn.zcard(&rate_limit_key)
            .map_err(|e| LockoutError::RedisError(e.to_string()))?;

        let attempts_remaining = self.policy.ip_rate_limit_max_attempts.saturating_sub(current_attempts);
        let reset_time = Utc::now() + self.policy.ip_rate_limit_window;

        let is_rate_limited = current_attempts >= self.policy.ip_rate_limit_max_attempts;
        let retry_after_seconds = if is_rate_limited {
            self.policy.ip_rate_limit_window.num_seconds() as u64
        } else {
            0
        };

        Ok(RateLimitInfo {
            is_rate_limited,
            attempts_remaining,
            reset_time,
            retry_after_seconds,
        })
    }

    /// Record a failed login attempt
    pub async fn record_failed_attempt(
        &self,
        user_id: Uuid,
        ip_address: &str,
        reason: &str
    ) -> Result<LockoutInfo, LockoutError> {
        let mut conn = self.get_redis_connection()?;
        
        let attempts_key = format!("failed_attempts:user:{}", user_id);
        let lockout_key = format!("lockout:user:{}", user_id);
        let ip_rate_limit_key = format!("rate_limit:ip:{}", ip_address);

        // Increment failed attempts counter
        let failed_attempts: u32 = conn.incr(&attempts_key, 1)
            .map_err(|e| LockoutError::RedisError(e.to_string()))?;

        // Set expiry for attempts counter (24 hours)
        let _: () = conn.expire(&attempts_key, 86400)
            .map_err(|e| LockoutError::RedisError(e.to_string()))?;

        // Record IP attempt for rate limiting
        let now = Utc::now().timestamp();
        let _: () = conn.zadd(&ip_rate_limit_key, now, now)
            .map_err(|e| LockoutError::RedisError(e.to_string()))?;
        let _: () = conn.expire(&ip_rate_limit_key, self.policy.ip_rate_limit_window.num_seconds())
            .map_err(|e| LockoutError::RedisError(e.to_string()))?;

        // Check if we need to lock the account
        if failed_attempts >= self.policy.max_failed_attempts {
            let lockout_duration = self.calculate_lockout_duration(failed_attempts);
            let locked_until = Utc::now() + lockout_duration;

            // Set lockout
            let _: () = conn.set_ex(
                &lockout_key, 
                locked_until.to_rfc3339(), 
                lockout_duration.num_seconds() as u64
            ).map_err(|e| LockoutError::RedisError(e.to_string()))?;

            // Log the lockout event to database
            self.log_lockout_event(user_id, ip_address, locked_until, reason).await?;

            return Ok(LockoutInfo {
                is_locked: true,
                locked_until: Some(locked_until),
                failed_attempts,
                lockout_reason: Some(format!("Account locked due to {} failed attempts", failed_attempts)),
                next_attempt_allowed_at: Some(locked_until),
            });
        }

        Ok(LockoutInfo {
            is_locked: false,
            locked_until: None,
            failed_attempts,
            lockout_reason: None,
            next_attempt_allowed_at: None,
        })
    }

    /// Record a successful login (clears failed attempts)
    pub async fn record_successful_attempt(&self, user_id: Uuid) -> Result<(), LockoutError> {
        let mut conn = self.get_redis_connection()?;
        
        let attempts_key = format!("failed_attempts:user:{}", user_id);
        let lockout_key = format!("lockout:user:{}", user_id);

        // Clear failed attempts and lockout
        let _: () = conn.del(&attempts_key)
            .map_err(|e| LockoutError::RedisError(e.to_string()))?;
        let _: () = conn.del(&lockout_key)
            .map_err(|e| LockoutError::RedisError(e.to_string()))?;

        Ok(())
    }

    /// Manually unlock an account (admin function)
    pub async fn unlock_account(&self, user_id: Uuid, admin_user_id: Uuid) -> Result<(), LockoutError> {
        let mut conn = self.get_redis_connection()?;
        
        let attempts_key = format!("failed_attempts:user:{}", user_id);
        let lockout_key = format!("lockout:user:{}", user_id);

        // Clear lockout and attempts
        let _: () = conn.del(&lockout_key)
            .map_err(|e| LockoutError::RedisError(e.to_string()))?;
        let _: () = conn.del(&attempts_key)
            .map_err(|e| LockoutError::RedisError(e.to_string()))?;

        // Log the unlock event
        self.log_unlock_event(user_id, admin_user_id).await?;

        Ok(())
    }

    /// Check if an IP should be blocked due to suspicious activity
    pub async fn check_suspicious_activity(&self, ip_address: &str) -> Result<bool, LockoutError> {
        let mut conn = self.get_redis_connection()?;
        
        let suspicious_key = format!("suspicious:ip:{}", ip_address);
        let is_suspicious: bool = conn.exists(&suspicious_key)
            .map_err(|e| LockoutError::RedisError(e.to_string()))?;

        Ok(is_suspicious)
    }

    /// Mark an IP as suspicious
    pub async fn mark_suspicious_ip(&self, ip_address: &str, duration: Duration) -> Result<(), LockoutError> {
        let mut conn = self.get_redis_connection()?;
        
        let suspicious_key = format!("suspicious:ip:{}", ip_address);
        let _: () = conn.set_ex(&suspicious_key, "true", duration.num_seconds() as u64)
            .map_err(|e| LockoutError::RedisError(e.to_string()))?;

        Ok(())
    }

    // Private helper methods

    fn get_redis_connection(&self) -> Result<Connection, LockoutError> {
        self.redis_client.get_connection()
            .map_err(|e| LockoutError::RedisError(e.to_string()))
    }

    fn calculate_lockout_duration(&self, failed_attempts: u32) -> Duration {
        let lockout_index = failed_attempts.saturating_sub(self.policy.max_failed_attempts) as usize;

        if lockout_index < self.policy.lockout_durations.len() {
            self.policy.lockout_durations[lockout_index]
        } else {
            // Use the last (maximum) duration for excessive attempts
            *self.policy.lockout_durations.last().unwrap_or(&Duration::hours(24))
        }
    }

    async fn log_lockout_event(
        &self,
        user_id: Uuid,
        ip_address: &str,
        locked_until: DateTime<Utc>,
        reason: &str,
    ) -> Result<(), LockoutError> {
        let query = r#"
            INSERT INTO security_events (
                id, user_id, event_type, event_data, ip_address,
                created_at, severity, description
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        "#;

        let event_data = serde_json::json!({
            "locked_until": locked_until.to_rfc3339(),
            "reason": reason,
            "lockout_type": "automatic"
        });

        sqlx::query(query)
            .bind(Uuid::new_v4())
            .bind(user_id)
            .bind("ACCOUNT_LOCKED")
            .bind(event_data)
            .bind(ip_address)
            .bind(Utc::now())
            .bind("HIGH")
            .bind(format!("Account locked until {} due to: {}", locked_until.format("%Y-%m-%d %H:%M:%S UTC"), reason))
            .execute(&self.db_pool)
            .await
            .map_err(|e| LockoutError::DatabaseError(e.to_string()))?;

        Ok(())
    }

    async fn log_unlock_event(
        &self,
        user_id: Uuid,
        admin_user_id: Uuid,
    ) -> Result<(), LockoutError> {
        let query = r#"
            INSERT INTO security_events (
                id, user_id, event_type, event_data, created_at,
                severity, description, performed_by
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        "#;

        let event_data = serde_json::json!({
            "unlock_type": "manual",
            "admin_user_id": admin_user_id
        });

        sqlx::query(query)
            .bind(Uuid::new_v4())
            .bind(user_id)
            .bind("ACCOUNT_UNLOCKED")
            .bind(event_data)
            .bind(Utc::now())
            .bind("MEDIUM")
            .bind("Account manually unlocked by administrator")
            .bind(admin_user_id)
            .execute(&self.db_pool)
            .await
            .map_err(|e| LockoutError::DatabaseError(e.to_string()))?;

        Ok(())
    }

    /// Get lockout statistics for monitoring
    pub async fn get_lockout_stats(&self, time_window: Duration) -> Result<LockoutStats, LockoutError> {
        let since = Utc::now() - time_window;

        let query = r#"
            SELECT
                COUNT(*) FILTER (WHERE event_type = 'ACCOUNT_LOCKED') as total_lockouts,
                COUNT(*) FILTER (WHERE event_type = 'ACCOUNT_UNLOCKED') as total_unlocks,
                COUNT(DISTINCT user_id) FILTER (WHERE event_type = 'ACCOUNT_LOCKED') as unique_users_locked,
                COUNT(DISTINCT ip_address) FILTER (WHERE event_type = 'ACCOUNT_LOCKED') as unique_ips_locked
            FROM security_events
            WHERE created_at >= $1
            AND event_type IN ('ACCOUNT_LOCKED', 'ACCOUNT_UNLOCKED')
        "#;

        let row: (i64, i64, i64, i64) = sqlx::query_as(query)
            .bind(since)
            .fetch_one(&self.db_pool)
            .await
            .map_err(|e| LockoutError::DatabaseError(e.to_string()))?;

        Ok(LockoutStats {
            total_lockouts: row.0 as u32,
            total_unlocks: row.1 as u32,
            unique_users_locked: row.2 as u32,
            unique_ips_locked: row.3 as u32,
            time_window,
        })
    }

    /// Clean up expired entries (maintenance function)
    pub async fn cleanup_expired_entries(&self) -> Result<u32, LockoutError> {
        let mut conn = self.get_redis_connection()?;
        let mut cleaned_count = 0u32;

        // Get all keys matching our patterns
        let patterns = vec![
            "lockout:user:*",
            "failed_attempts:user:*",
            "rate_limit:ip:*",
            "suspicious:ip:*",
        ];

        for pattern in patterns {
            let keys: Vec<String> = conn.keys(pattern)
                .map_err(|e| LockoutError::RedisError(e.to_string()))?;

            for key in keys {
                // Check if key has TTL, if not it might be stuck
                let ttl: i64 = conn.ttl(&key)
                    .map_err(|e| LockoutError::RedisError(e.to_string()))?;

                if ttl == -1 {
                    // Key exists but has no expiry, clean it up
                    let _: () = conn.del(&key)
                        .map_err(|e| LockoutError::RedisError(e.to_string()))?;
                    cleaned_count += 1;
                }
            }
        }

        Ok(cleaned_count)
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LockoutStats {
    pub total_lockouts: u32,
    pub total_unlocks: u32,
    pub unique_users_locked: u32,
    pub unique_ips_locked: u32,
    pub time_window: Duration,
}
