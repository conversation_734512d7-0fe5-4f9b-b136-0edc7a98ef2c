use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;
use chrono::{DateTime, Utc};
use validator::Validate;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, FromRow)]
pub struct UserMfaSecret {
    pub id: Uuid,
    pub user_id: Uuid,
    pub secret_key: String,
    pub is_enabled: bool,
    pub backup_codes: Vec<String>,
    pub backup_codes_used: Vec<String>,
    
    // TOTP configuration
    pub algorithm: String,
    pub digits: i32,
    pub period: i32,
    
    // Recovery and security
    pub recovery_codes_generated_at: Option<DateTime<Utc>>,
    pub last_used_at: Option<DateTime<Utc>>,
    
    // Audit fields
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub enabled_at: Option<DateTime<Utc>>,
    pub disabled_at: Option<DateTime<Utc>>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, FromRow)]
pub struct TrustedDevice {
    pub id: Uuid,
    pub user_id: Uuid,
    pub device_fingerprint: String,
    pub device_name: Option<String>,
    pub device_type: Option<String>, // mobile, desktop, tablet
    
    // Trust details
    pub trust_level: String, // PARTIAL, FULL
    pub trusted_until: Option<DateTime<Utc>>,
    
    // Device information
    pub browser_name: Option<String>,
    pub browser_version: Option<String>,
    pub os_name: Option<String>,
    pub os_version: Option<String>,
    
    // Location and network
    pub ip_address: Option<String>,
    pub country_code: Option<String>,
    pub city: Option<String>,
    
    // Usage tracking
    pub last_used_at: DateTime<Utc>,
    pub usage_count: i32,
    
    // Lifecycle
    pub created_at: DateTime<Utc>,
    pub expires_at: Option<DateTime<Utc>>,
    pub revoked_at: Option<DateTime<Utc>>,
    pub revoked_reason: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct MfaVerificationAttempt {
    pub id: Uuid,
    pub user_id: Uuid,
    pub session_id: Option<Uuid>,
    
    // Verification details
    pub verification_type: String, // TOTP, BACKUP_CODE, SMS
    pub success: bool,
    pub failure_reason: Option<String>,
    
    // Context
    pub ip_address: Option<String>,
    pub user_agent: Option<String>,
    pub device_fingerprint: Option<String>,
    
    // Timing
    pub attempted_at: DateTime<Utc>,
    pub verified_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct MfaBackupCode {
    pub id: Uuid,
    pub user_id: Uuid,
    pub code_hash: String,
    pub is_used: bool,
    pub used_at: Option<DateTime<Utc>>,
    pub used_ip: Option<String>,
    pub created_at: DateTime<Utc>,
}

// Request/Response DTOs

#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct MfaSetupRequest {
    #[validate(length(min = 1, max = 100, message = "Device name must be between 1 and 100 characters"))]
    pub device_name: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MfaSetupResponse {
    pub secret: String,
    pub qr_code: String, // Base64 encoded QR code image
    pub backup_codes: Vec<String>,
    pub setup_uri: String,
}

#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct MfaVerifySetupRequest {
    #[validate(length(equal = 6, message = "TOTP code must be exactly 6 digits"))]
    pub totp_code: String,
}

#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct MfaVerifyRequest {
    #[validate(length(min = 1, max = 20, message = "Code must be between 1 and 20 characters"))]
    pub code: String,
    #[validate(custom(function = "validate_verification_type"))]
    pub verification_type: String, // TOTP, BACKUP_CODE
    pub trust_device: Option<bool>,
}

/// Custom validator for MFA verification type
fn validate_verification_type(verification_type: &str) -> Result<(), validator::ValidationError> {
    match verification_type {
        "TOTP" | "BACKUP_CODE" => Ok(()),
        _ => Err(validator::ValidationError::new("Invalid verification type. Must be 'TOTP' or 'BACKUP_CODE'")),
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MfaVerifyResponse {
    pub success: bool,
    pub device_trusted: bool,
    pub backup_codes_remaining: Option<i32>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MfaStatusResponse {
    pub is_enabled: bool,
    pub backup_codes_count: i32,
    pub last_used_at: Option<DateTime<Utc>>,
    pub trusted_devices_count: i32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GenerateBackupCodesResponse {
    pub backup_codes: Vec<String>,
    pub generated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TrustedDeviceResponse {
    pub id: Uuid,
    pub device_name: Option<String>,
    pub device_type: Option<String>,
    pub browser_name: Option<String>,
    pub os_name: Option<String>,
    pub country_code: Option<String>,
    pub city: Option<String>,
    pub last_used_at: DateTime<Utc>,
    pub created_at: DateTime<Utc>,
    pub expires_at: Option<DateTime<Utc>>,
    pub trust_level: String,
}

impl From<TrustedDevice> for TrustedDeviceResponse {
    fn from(device: TrustedDevice) -> Self {
        Self {
            id: device.id,
            device_name: device.device_name,
            device_type: device.device_type,
            browser_name: device.browser_name,
            os_name: device.os_name,
            country_code: device.country_code,
            city: device.city,
            last_used_at: device.last_used_at,
            created_at: device.created_at,
            expires_at: device.expires_at,
            trust_level: device.trust_level,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TrustDeviceRequest {
    pub device_fingerprint: String,
    pub device_name: Option<String>,
    pub device_type: Option<String>,
    pub browser_name: Option<String>,
    pub browser_version: Option<String>,
    pub os_name: Option<String>,
    pub os_version: Option<String>,
    pub trust_level: String,
    pub trust_duration_days: Option<i32>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RevokeTrustedDeviceRequest {
    pub device_id: Uuid,
    pub reason: Option<String>,
}
